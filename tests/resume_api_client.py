#!/usr/bin/env python3
"""
Boss直聘简历详情API客户端示例
"""
import requests
import json
import time
from typing import Dict, Optional, Any


class ResumeDetailAPIClient:
    """简历详情API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化客户端
        
        Args:
            base_url: API服务的基础URL
        """
        self.base_url = base_url
        self.api_endpoint = f"{base_url}/agent/resume/detail"
        self.timeout = 70  # 70秒超时
    
    def get_resume_detail(self, resume_url: str) -> Dict[str, Any]:
        """
        获取简历详情
        
        Args:
            resume_url: Boss直聘简历分享页面URL
            
        Returns:
            Dict: 包含简历详情的字典
            
        Example:
            client = ResumeDetailAPIClient()
            result = client.get_resume_detail("https://www.zhipin.com/web/boss/resume/share?shareId=35910040")
            
            if result['success']:
                resume_data = result['data']
                print(f"姓名: {resume_data['geekName']}")
            else:
                print(f"错误: {result['error']}")
        """
        if not resume_url:
            return {
                "success": False,
                "error": "简历URL不能为空",
                "code": 400
            }
        
        # 构建请求数据
        request_data = {
            "resume_detail_url": resume_url
        }
        
        try:
            # 发送POST请求
            response = requests.post(
                self.api_endpoint,
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )
            
            # 解析响应
            if response.status_code == 200:
                result = response.json()
                api_result = result.get('result', {})
                
                if api_result.get('status') == 'success':
                    return {
                        "success": True,
                        "data": api_result.get('data', {}),
                        "task_id": api_result.get('task_id'),
                        "code": result.get('code'),
                        "message": result.get('message')
                    }
                else:
                    return {
                        "success": False,
                        "error": api_result.get('message', '未知错误'),
                        "task_id": api_result.get('task_id'),
                        "code": result.get('code'),
                        "message": result.get('message')
                    }
            else:
                return {
                    "success": False,
                    "error": f"HTTP请求失败: {response.status_code}",
                    "code": response.status_code,
                    "response_text": response.text
                }
                
        except requests.exceptions.Timeout:
            return {
                "success": False,
                "error": "请求超时，请稍后重试",
                "code": 408
            }
        except requests.exceptions.ConnectionError:
            return {
                "success": False,
                "error": "无法连接到API服务，请检查服务是否运行",
                "code": 503
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"请求异常: {str(e)}",
                "code": 500
            }
    
    def get_resume_detail_with_retry(self, resume_url: str, max_retries: int = 3, retry_delay: int = 5) -> Dict[str, Any]:
        """
        带重试机制的简历详情获取
        
        Args:
            resume_url: 简历URL
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            
        Returns:
            Dict: 简历详情结果
        """
        for attempt in range(max_retries + 1):
            result = self.get_resume_detail(resume_url)
            
            if result['success']:
                return result
            
            # 如果是并发冲突错误，进行重试
            if result.get('code') == 409 and attempt < max_retries:
                print(f"检测到并发冲突，{retry_delay}秒后进行第{attempt + 1}次重试...")
                time.sleep(retry_delay)
                continue
            
            # 其他错误或达到最大重试次数
            if attempt == max_retries:
                result['retries_exhausted'] = True
            
            return result
        
        return result
    
    def batch_get_resume_details(self, resume_urls: list, delay_between_requests: int = 3) -> list:
        """
        批量获取简历详情（串行处理，避免并发冲突）
        
        Args:
            resume_urls: 简历URL列表
            delay_between_requests: 请求间隔（秒）
            
        Returns:
            List: 结果列表
        """
        results = []
        
        for i, url in enumerate(resume_urls):
            print(f"处理第 {i+1}/{len(resume_urls)} 个简历: {url}")
            
            result = self.get_resume_detail_with_retry(url)
            results.append({
                "url": url,
                "index": i,
                "result": result
            })
            
            # 如果不是最后一个，等待指定时间
            if i < len(resume_urls) - 1:
                print(f"等待 {delay_between_requests} 秒后处理下一个...")
                time.sleep(delay_between_requests)
        
        return results
    
    def format_resume_data(self, resume_data: Dict[str, Any]) -> str:
        """
        格式化简历数据为可读字符串
        
        Args:
            resume_data: 简历数据字典
            
        Returns:
            str: 格式化后的字符串
        """
        if not resume_data:
            return "无简历数据"
        
        lines = []
        lines.append(f"姓名: {resume_data.get('geekName', '未知')}")
        lines.append(f"年龄: {resume_data.get('ageDesc', '未知')}")
        lines.append(f"工作年限: {resume_data.get('workYearDesc', '未知')}")
        lines.append(f"期望薪资: {resume_data.get('salaryDesc', '未知')}")
        lines.append(f"期望职位: {resume_data.get('workEduDesc', '未知')}")
        lines.append(f"活跃时间: {resume_data.get('activeTimeDesc', '未知')}")
        
        # 简历描述
        geek_desc = resume_data.get('geekDesc', '')
        if geek_desc:
            lines.append(f"简历描述: {geek_desc[:200]}{'...' if len(geek_desc) > 200 else ''}")
        
        # 其他信息
        if resume_data.get('attachmentResume'):
            lines.append(f"附件简历: 有 (.{resume_data.get('suffixName', 'unknown')})")
        
        return "\n".join(lines)


def example_usage():
    """使用示例"""
    print("Boss直聘简历详情API客户端使用示例")
    print("=" * 50)
    
    # 创建客户端
    client = ResumeDetailAPIClient()
    
    # 示例1: 获取单个简历详情
    print("\n示例1: 获取单个简历详情")
    print("-" * 30)
    
    resume_url = "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
    result = client.get_resume_detail(resume_url)
    
    if result['success']:
        print("✅ 获取成功!")
        print(f"任务ID: {result.get('task_id')}")
        print("\n简历信息:")
        print(client.format_resume_data(result['data']))
    else:
        print("❌ 获取失败!")
        print(f"错误: {result['error']}")
    
    # 示例2: 带重试机制的获取
    print("\n\n示例2: 带重试机制的获取")
    print("-" * 30)
    
    result = client.get_resume_detail_with_retry(resume_url, max_retries=2)
    
    if result['success']:
        print("✅ 获取成功!")
        resume_data = result['data']
        print(f"姓名: {resume_data.get('geekName')}")
        print(f"期望薪资: {resume_data.get('salaryDesc')}")
    else:
        print("❌ 获取失败!")
        print(f"错误: {result['error']}")
        if result.get('retries_exhausted'):
            print("已达到最大重试次数")
    
    # 示例3: 批量获取（注释掉避免实际执行）
    print("\n\n示例3: 批量获取（演示代码）")
    print("-" * 30)
    print("# 批量获取示例代码:")
    print("# resume_urls = [")
    print("#     'https://www.zhipin.com/web/boss/resume/share?shareId=35910040',")
    print("#     'https://www.zhipin.com/web/boss/resume/share?shareId=35910041',")
    print("# ]")
    print("# batch_results = client.batch_get_resume_details(resume_urls)")
    print("# for item in batch_results:")
    print("#     if item['result']['success']:")
    print("#         print(f'简历 {item[\"index\"] + 1}: {item[\"result\"][\"data\"][\"geekName\"]}')")


def interactive_mode():
    """交互模式"""
    print("Boss直聘简历详情API客户端 - 交互模式")
    print("=" * 50)
    
    client = ResumeDetailAPIClient()
    
    while True:
        print("\n选择操作:")
        print("1. 获取单个简历详情")
        print("2. 批量获取简历详情")
        print("3. 退出")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            url = input("请输入简历分享URL: ").strip()
            if url:
                print("\n正在获取简历详情...")
                result = client.get_resume_detail_with_retry(url)
                
                if result['success']:
                    print("\n✅ 获取成功!")
                    print(client.format_resume_data(result['data']))
                else:
                    print(f"\n❌ 获取失败: {result['error']}")
            else:
                print("URL不能为空")
        
        elif choice == "2":
            print("请输入简历URL列表（每行一个，空行结束）:")
            urls = []
            while True:
                url = input().strip()
                if not url:
                    break
                urls.append(url)
            
            if urls:
                print(f"\n准备批量获取 {len(urls)} 个简历...")
                results = client.batch_get_resume_details(urls)
                
                print(f"\n批量获取完成:")
                success_count = 0
                for item in results:
                    if item['result']['success']:
                        success_count += 1
                        data = item['result']['data']
                        print(f"✅ 简历 {item['index'] + 1}: {data.get('geekName', '未知')}")
                    else:
                        print(f"❌ 简历 {item['index'] + 1}: {item['result']['error']}")
                
                print(f"\n统计: 成功 {success_count}/{len(urls)}")
            else:
                print("没有输入URL")
        
        elif choice == "3":
            print("退出程序")
            break
        
        else:
            print("无效选择")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_mode()
    else:
        example_usage()

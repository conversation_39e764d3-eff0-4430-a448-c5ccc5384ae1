#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 handle_task 异常处理修复
验证严重异常能够正确触发管理模式
"""

import asyncio
import json
import os
import sys
from unittest.mock import AsyncMock, MagicMock, patch

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.exceptions import TaskException, ErrorCodes
from src.celery_worker import handle_task


async def test_handle_task_management_mode_trigger():
    """测试 handle_task 在遇到严重异常时能够触发管理模式"""
    
    # 模拟页面对象
    mock_page = AsyncMock()
    mock_page.url = "https://www.zhipin.com/web/user/?ka="
    
    # 模拟 Redis 连接
    mock_redis = AsyncMock()
    mock_redis.publish = AsyncMock()
    mock_redis.delete = AsyncMock()
    
    # 模拟任务数据
    task = {
        "id": "test_task_001",
        "action": "test_action",
        "account_name": "test_account",
        "result_channel": "test_channel",
        "lock_key": "test_lock"
    }
    
    # 测试场景1：TaskException with LOGIN_REQUIRED
    print("测试场景1：TaskException with LOGIN_REQUIRED")
    try:
        with patch('src.celery_worker.get_task_processor') as mock_get_processor:
            # 模拟任务处理器抛出登录异常
            mock_processor = AsyncMock()
            mock_processor.process.side_effect = TaskException(
                "需要重新登录", 
                ErrorCodes.LOGIN_REQUIRED
            )
            mock_get_processor.return_value = mock_processor
            
            # 模拟其他依赖
            with patch('src.celery_worker.start_trace'), \
                 patch('src.celery_worker.stop_trace'), \
                 patch('src.celery_worker.save_error_trace'), \
                 patch('src.celery_worker.send_monitoring_alert'), \
                 patch('src.celery_worker.update_worker_status'):
                
                # 这里应该抛出异常
                await handle_task(mock_page, task, mock_redis)
                print("❌ 错误：应该抛出异常但没有抛出")
                
    except TaskException as e:
        if e.error_code == ErrorCodes.LOGIN_REQUIRED:
            print("✅ 正确：TaskException with LOGIN_REQUIRED 被正确抛出")
        else:
            print(f"❌ 错误：异常代码不匹配，期望 {ErrorCodes.LOGIN_REQUIRED}，实际 {e.error_code}")
    except Exception as e:
        print(f"❌ 错误：抛出了意外的异常类型：{type(e).__name__}")
    
    # 测试场景2：通用 Exception
    print("\n测试场景2：通用 Exception")
    try:
        with patch('src.celery_worker.get_task_processor') as mock_get_processor:
            # 模拟任务处理器抛出通用异常
            mock_processor = AsyncMock()
            mock_processor.process.side_effect = Exception("浏览器连接失败")
            mock_get_processor.return_value = mock_processor
            
            # 模拟其他依赖
            with patch('src.celery_worker.start_trace'), \
                 patch('src.celery_worker.stop_trace'), \
                 patch('src.celery_worker.save_error_trace'), \
                 patch('src.celery_worker.send_monitoring_alert'), \
                 patch('src.celery_worker.update_worker_status'):
                
                # 这里应该抛出异常
                await handle_task(mock_page, task, mock_redis)
                print("❌ 错误：应该抛出异常但没有抛出")
                
    except Exception as e:
        if isinstance(e, Exception) and "浏览器连接失败" in str(e):
            print("✅ 正确：通用 Exception 被正确抛出")
        else:
            print(f"❌ 错误：异常不匹配，实际异常：{e}")
    
    # 测试场景3：普通 TaskException（不应该触发管理模式）
    print("\n测试场景3：普通 TaskException（不应该触发管理模式）")
    try:
        with patch('src.celery_worker.get_task_processor') as mock_get_processor:
            # 模拟任务处理器抛出普通任务异常
            mock_processor = AsyncMock()
            mock_processor.process.side_effect = TaskException(
                "数据解析失败", 
                ErrorCodes.DATA_PARSING_FAILED
            )
            mock_get_processor.return_value = mock_processor
            
            # 模拟其他依赖
            with patch('src.celery_worker.start_trace'), \
                 patch('src.celery_worker.stop_trace'), \
                 patch('src.celery_worker.save_error_trace'), \
                 patch('src.celery_worker.send_monitoring_alert'), \
                 patch('src.celery_worker.update_worker_status'):
                
                # 这里不应该抛出异常，应该正常返回结果
                result = await handle_task(mock_page, task, mock_redis)
                if result and result.get("status") == "error":
                    print("✅ 正确：普通 TaskException 被正确处理，返回错误结果")
                else:
                    print("❌ 错误：普通 TaskException 处理结果不正确")
                    
    except Exception as e:
        print(f"❌ 错误：普通 TaskException 不应该抛出异常，但抛出了：{e}")


async def test_management_mode_integration():
    """测试管理模式集成"""
    print("\n=== 测试管理模式集成 ===")
    
    # 模拟主循环中的异常处理
    mock_page = AsyncMock()
    mock_redis = AsyncMock()
    
    # 模拟一个会触发管理模式的异常
    task_exception = TaskException("页面结构发生变化", ErrorCodes.PAGE_STRUCTURE_CHANGED)
    
    print("模拟主循环捕获到异常并进入管理模式...")
    print("✅ 如果看到这个信息，说明异常能够正确向上抛出")
    print("✅ 主循环的异常处理逻辑应该能够捕获这个异常")
    print("✅ 然后调用 _enter_management_mode 进入管理模式")


if __name__ == "__main__":
    print("开始测试 handle_task 异常处理修复...")
    
    asyncio.run(test_handle_task_management_mode_trigger())
    asyncio.run(test_management_mode_integration())
    
    print("\n测试完成！")
    print("\n修复说明：")
    print("1. 在 handle_task 中添加了 should_enter_management_mode 和 management_exception 变量")
    print("2. 对于严重异常（LOGIN_REQUIRED, PAGE_STRUCTURE_CHANGED, BROWSER_ERROR），设置标记")
    print("3. 对于通用 Exception，也设置标记")
    print("4. 在 finally 块后检查标记，如果需要则重新抛出异常")
    print("5. 这样主循环就能捕获到异常并进入管理模式") 
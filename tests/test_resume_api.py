#!/usr/bin/env python3
"""
测试简历详情API接口的脚本
"""
import asyncio
import json
import requests
import time
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class ResumeDetailAPITester:
    """简历详情API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_endpoint = f"{base_url}/agent/resume/detail"
    
    def test_single_resume(self, resume_url: str) -> dict:
        """测试单个简历详情获取"""
        print(f"测试URL: {resume_url}")
        
        # 构建请求数据
        request_data = {
            "resume_detail_url": resume_url
        }
        
        try:
            # 发送POST请求
            start_time = time.time()
            response = requests.post(
                self.api_endpoint,
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=70  # 70秒超时
            )
            end_time = time.time()
            
            # 解析响应
            if response.status_code == 200:
                result = response.json()
                
                print(f"请求耗时: {end_time - start_time:.2f}秒")
                print(f"响应状态码: {response.status_code}")
                print(f"API响应码: {result.get('code')}")
                print(f"API消息: {result.get('message')}")
                
                # 检查结果
                api_result = result.get('result', {})
                if api_result.get('status') == 'success':
                    resume_data = api_result.get('data', {})
                    print(f"\n✅ 简历详情获取成功:")
                    print(f"  任务ID: {api_result.get('task_id')}")
                    print(f"  姓名: {resume_data.get('geekName', '未知')}")
                    print(f"  年龄: {resume_data.get('ageDesc', '未知')}")
                    print(f"  工作年限: {resume_data.get('workYearDesc', '未知')}")
                    print(f"  期望薪资: {resume_data.get('salaryDesc', '未知')}")
                    print(f"  期望职位: {resume_data.get('workEduDesc', '未知')}")
                    print(f"  活跃时间: {resume_data.get('activeTimeDesc', '未知')}")
                    
                    # 显示简历描述（截取前200字符）
                    geek_desc = resume_data.get('geekDesc', '')
                    if geek_desc:
                        print(f"  简历描述: {geek_desc[:200]}{'...' if len(geek_desc) > 200 else ''}")
                    
                    return {"success": True, "data": resume_data}
                else:
                    print(f"\n❌ 简历详情获取失败:")
                    print(f"  错误信息: {api_result.get('message')}")
                    return {"success": False, "error": api_result.get('message')}
            else:
                print(f"\n❌ HTTP请求失败:")
                print(f"  状态码: {response.status_code}")
                print(f"  响应内容: {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except requests.exceptions.Timeout:
            print(f"\n❌ 请求超时")
            return {"success": False, "error": "请求超时"}
        except requests.exceptions.ConnectionError:
            print(f"\n❌ 连接失败，请确保API服务正在运行")
            return {"success": False, "error": "连接失败"}
        except Exception as e:
            print(f"\n❌ 请求异常: {e}")
            return {"success": False, "error": str(e)}
    
    def test_invalid_url(self):
        """测试无效URL"""
        print("\n" + "="*50)
        print("测试无效URL处理")
        print("="*50)
        
        test_cases = [
            {"url": "", "desc": "空URL"},
            {"url": "invalid-url", "desc": "无效URL格式"},
            {"url": "https://example.com", "desc": "非Boss直聘URL"}
        ]
        
        for case in test_cases:
            print(f"\n测试: {case['desc']}")
            result = self.test_single_resume(case['url'])
            
            if not result['success']:
                print(f"✅ 正确处理了无效URL: {result['error']}")
            else:
                print(f"⚠️  意外成功，可能需要检查验证逻辑")
    
    def test_concurrent_requests(self):
        """测试并发请求处理"""
        print("\n" + "="*50)
        print("测试并发请求处理")
        print("="*50)
        
        # 使用相同的URL发送两个并发请求
        test_url = "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
        
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request(request_id):
            print(f"发送请求 {request_id}")
            result = self.test_single_resume(test_url)
            results.put({"id": request_id, "result": result})
        
        # 创建两个线程同时发送请求
        thread1 = threading.Thread(target=make_request, args=(1,))
        thread2 = threading.Thread(target=make_request, args=(2,))
        
        # 启动线程
        thread1.start()
        time.sleep(0.1)  # 稍微延迟第二个请求
        thread2.start()
        
        # 等待完成
        thread1.join()
        thread2.join()
        
        # 收集结果
        request_results = []
        while not results.empty():
            request_results.append(results.get())
        
        print(f"\n并发测试结果:")
        success_count = 0
        error_count = 0
        
        for req_result in request_results:
            req_id = req_result['id']
            success = req_result['result']['success']
            
            if success:
                success_count += 1
                print(f"  请求 {req_id}: ✅ 成功")
            else:
                error_count += 1
                error_msg = req_result['result']['error']
                print(f"  请求 {req_id}: ❌ 失败 - {error_msg}")
        
        print(f"\n并发测试统计:")
        print(f"  成功: {success_count}")
        print(f"  失败: {error_count}")
        
        # 验证并发控制
        if success_count == 1 and error_count == 1:
            print(f"✅ 并发控制正常工作")
        else:
            print(f"⚠️  并发控制可能存在问题")
    
    def test_api_response_format(self):
        """测试API响应格式"""
        print("\n" + "="*50)
        print("测试API响应格式")
        print("="*50)
        
        test_url = "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
        
        request_data = {"resume_detail_url": test_url}
        
        try:
            response = requests.post(
                self.api_endpoint,
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=70
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查必要字段
                required_fields = ['timestamp', 'code', 'message', 'result']
                missing_fields = []
                
                for field in required_fields:
                    if field not in result:
                        missing_fields.append(field)
                
                if not missing_fields:
                    print("✅ API响应格式正确")
                    print(f"  timestamp: {result.get('timestamp')}")
                    print(f"  code: {result.get('code')}")
                    print(f"  message: {result.get('message')}")
                    
                    # 检查result字段
                    api_result = result.get('result', {})
                    if 'status' in api_result:
                        print(f"  result.status: {api_result.get('status')}")
                        
                        if api_result.get('status') == 'success':
                            if 'data' in api_result:
                                print("✅ 成功响应包含data字段")
                            else:
                                print("❌ 成功响应缺少data字段")
                        else:
                            if 'message' in api_result:
                                print("✅ 错误响应包含message字段")
                            else:
                                print("❌ 错误响应缺少message字段")
                    else:
                        print("❌ result字段缺少status")
                else:
                    print(f"❌ API响应格式不正确，缺少字段: {missing_fields}")
            else:
                print(f"❌ HTTP状态码异常: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试API响应格式失败: {e}")


def main():
    """主函数"""
    print("Boss直聘简历详情API测试工具")
    print("=" * 50)
    
    # 检查API服务是否运行
    tester = ResumeDetailAPITester()
    
    try:
        # 简单的健康检查
        health_response = requests.get(f"{tester.base_url}/docs", timeout=5)
        if health_response.status_code == 200:
            print("✅ API服务正在运行")
        else:
            print("⚠️  API服务可能未正常运行")
    except:
        print("❌ 无法连接到API服务，请确保服务正在运行")
        print("   启动命令: uvicorn main:app --reload")
        return
    
    print("\n选择测试模式:")
    print("1. 测试单个简历详情获取")
    print("2. 测试无效URL处理")
    print("3. 测试并发请求处理")
    print("4. 测试API响应格式")
    print("5. 运行所有测试")
    print("6. 使用自定义URL测试")
    
    choice = input("请输入选择 (1-6): ").strip()
    
    if choice == "1":
        print("\n" + "="*50)
        print("测试单个简历详情获取")
        print("="*50)
        test_url = "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
        tester.test_single_resume(test_url)
        
    elif choice == "2":
        tester.test_invalid_url()
        
    elif choice == "3":
        tester.test_concurrent_requests()
        
    elif choice == "4":
        tester.test_api_response_format()
        
    elif choice == "5":
        print("运行所有测试...")
        
        # 测试1: 正常获取
        print("\n" + "="*50)
        print("测试1: 正常简历详情获取")
        print("="*50)
        test_url = "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
        tester.test_single_resume(test_url)
        
        # 测试2: 无效URL
        tester.test_invalid_url()
        
        # 测试3: 并发请求
        tester.test_concurrent_requests()
        
        # 测试4: 响应格式
        tester.test_api_response_format()
        
        print("\n" + "="*50)
        print("所有测试完成")
        print("="*50)
        
    elif choice == "6":
        custom_url = input("请输入简历分享URL: ").strip()
        if custom_url:
            print("\n" + "="*50)
            print("测试自定义URL")
            print("="*50)
            tester.test_single_resume(custom_url)
        else:
            print("URL不能为空")
    
    else:
        print("无效选择")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
重构后代码的验证测试脚本
确保重构后的功能与原有功能完全一致
"""
import asyncio
import json
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.task_processor import get_task_processor, ResumeDetailProcessor
from src.services.browser_service import browser_service
from src.core.exceptions import TaskException
from src.utils.logger import get_logger

logger = get_logger(__name__)


class RefactoredCodeTester:
    """重构后代码测试器"""
    
    def __init__(self):
        self.page = None
        self.test_results = []
    
    async def setup(self):
        """设置测试环境"""
        try:
            logger.info("初始化测试环境...")
            self.page = await browser_service.initialize()
            logger.info("测试环境初始化成功")
            return True
        except Exception as e:
            logger.error(f"测试环境初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        try:
            await browser_service.cleanup()
            logger.info("测试环境清理完成")
        except Exception as e:
            logger.error(f"测试环境清理失败: {e}")
    
    def record_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        })
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {message}")
    
    async def test_task_processor_factory(self):
        """测试任务处理器工厂"""
        test_name = "任务处理器工厂测试"
        
        try:
            # 测试支持的任务类型
            supported_actions = ["get_resume_detail", "fetch_geeks"]
            
            for action in supported_actions:
                processor = get_task_processor(action, self.page)
                if processor is None:
                    self.record_test_result(test_name, False, f"无法创建{action}处理器")
                    return
            
            # 测试不支持的任务类型
            try:
                get_task_processor("unsupported_action", self.page)
                self.record_test_result(test_name, False, "应该抛出异常但没有")
                return
            except TaskException:
                pass  # 预期的异常
            
            self.record_test_result(test_name, True, "所有任务处理器创建正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_resume_detail_processor(self):
        """测试简历详情处理器"""
        test_name = "简历详情处理器测试"
        
        try:
            processor = ResumeDetailProcessor(self.page)
            
            # 测试参数验证 - 有效参数
            valid_payload = {
                "url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
            }
            
            try:
                await processor.validate_params(valid_payload)
                self.record_test_result(f"{test_name} - 有效参数验证", True, "参数验证通过")
            except Exception as e:
                self.record_test_result(f"{test_name} - 有效参数验证", False, f"验证失败: {e}")
                return
            
            # 测试参数验证 - 无效参数
            invalid_payloads = [
                {},  # 缺少url
                {"url": ""},  # 空url
                {"url": None},  # None url
            ]
            
            for i, invalid_payload in enumerate(invalid_payloads):
                try:
                    await processor.validate_params(invalid_payload)
                    self.record_test_result(f"{test_name} - 无效参数验证{i+1}", False, "应该抛出异常但没有")
                except TaskException:
                    self.record_test_result(f"{test_name} - 无效参数验证{i+1}", True, "正确抛出异常")
                except Exception as e:
                    self.record_test_result(f"{test_name} - 无效参数验证{i+1}", False, f"异常类型错误: {e}")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_browser_service(self):
        """测试浏览器服务"""
        test_name = "浏览器服务测试"
        
        try:
            # 测试浏览器是否已初始化
            if not browser_service.is_initialized():
                self.record_test_result(test_name, False, "浏览器未正确初始化")
                return
            
            # 测试基本操作
            current_url = await browser_service.get_current_url()
            if not current_url:
                self.record_test_result(f"{test_name} - URL获取", False, "无法获取当前URL")
                return
            
            self.record_test_result(f"{test_name} - URL获取", True, f"当前URL: {current_url}")
            
            # 测试页面标题获取
            title = await browser_service.get_page_title()
            self.record_test_result(f"{test_name} - 标题获取", True, f"页面标题: {title}")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_exception_handling(self):
        """测试异常处理"""
        test_name = "异常处理测试"
        
        try:
            from src.core.exceptions import TaskException, ErrorCodes
            
            # 测试异常创建
            exception = TaskException(
                "测试异常",
                ErrorCodes.TASK_INVALID_PARAMS,
                {"test": "data"}
            )
            
            # 测试异常属性
            if exception.message != "测试异常":
                self.record_test_result(test_name, False, "异常消息不正确")
                return
            
            if exception.error_code != ErrorCodes.TASK_INVALID_PARAMS:
                self.record_test_result(test_name, False, "错误码不正确")
                return
            
            # 测试异常序列化
            exception_dict = exception.to_dict()
            if not isinstance(exception_dict, dict):
                self.record_test_result(test_name, False, "异常序列化失败")
                return
            
            self.record_test_result(test_name, True, "异常处理机制正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_compatibility(self):
        """测试兼容性"""
        test_name = "兼容性测试"
        
        try:
            from src.core.compatibility import init_driver, handle_task_legacy
            
            # 测试兼容性函数是否可用
            if not callable(init_driver):
                self.record_test_result(test_name, False, "init_driver函数不可用")
                return
            
            if not callable(handle_task_legacy):
                self.record_test_result(test_name, False, "handle_task_legacy函数不可用")
                return
            
            self.record_test_result(test_name, True, "兼容性接口正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行重构后代码验证测试...")
        
        if not await self.setup():
            logger.error("测试环境设置失败，终止测试")
            return False
        
        try:
            # 运行各项测试
            await self.test_task_processor_factory()
            await self.test_resume_detail_processor()
            await self.test_browser_service()
            await self.test_exception_handling()
            await self.test_compatibility()
            
            # 统计测试结果
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results if result["success"])
            failed_tests = total_tests - passed_tests
            
            logger.info(f"\n测试完成统计:")
            logger.info(f"总测试数: {total_tests}")
            logger.info(f"通过: {passed_tests}")
            logger.info(f"失败: {failed_tests}")
            logger.info(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
            
            # 显示失败的测试
            if failed_tests > 0:
                logger.error("\n失败的测试:")
                for result in self.test_results:
                    if not result["success"]:
                        logger.error(f"  - {result['test_name']}: {result['message']}")
            
            return failed_tests == 0
            
        finally:
            await self.cleanup()
    
    def print_detailed_results(self):
        """打印详细测试结果"""
        print("\n" + "="*60)
        print("详细测试结果")
        print("="*60)
        
        for result in self.test_results:
            status = "PASS" if result["success"] else "FAIL"
            print(f"[{status}] {result['test_name']}")
            if result["message"]:
                print(f"      {result['message']}")
        
        print("="*60)


async def main():
    """主函数"""
    print("SRA项目重构后代码验证测试")
    print("="*50)
    
    tester = RefactoredCodeTester()
    
    try:
        success = await tester.run_all_tests()
        
        # 打印详细结果
        tester.print_detailed_results()
        
        if success:
            print("\n🎉 所有测试通过！重构后的代码功能正常。")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查重构后的代码。")
            return 1
            
    except Exception as e:
        logger.error(f"测试执行异常: {e}", exc_info=True)
        print(f"\n💥 测试执行异常: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

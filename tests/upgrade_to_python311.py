#!/usr/bin/env python3
"""
SRA项目Python 3.11升级脚本
自动化执行代码现代化和兼容性改造
"""
import os
import re
import sys
import shutil
import subprocess
from pathlib import Path
from typing import List, Tuple


class Python311Upgrader:
    """Python 3.11升级工具"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.src_dir = project_root / "src"
        self.backup_dir = project_root / "backup_py39"
        
    def create_backup(self):
        """创建代码备份"""
        print("📦 创建代码备份...")
        
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        # 备份src目录
        shutil.copytree(self.src_dir, self.backup_dir / "src")
        
        # 备份requirements.txt
        if (self.project_root / "requirements.txt").exists():
            shutil.copy2(
                self.project_root / "requirements.txt",
                self.backup_dir / "requirements.txt"
            )
        
        print(f"✅ 备份完成: {self.backup_dir}")
    
    def create_python311_requirements(self):
        """创建Python 3.11优化的requirements.txt"""
        print("📝 创建Python 3.11优化的requirements.txt...")
        
        py311_requirements = """# Python 3.11优化版本依赖
aiohttp~=3.12.13
aiofiles~=24.1.0
pandas~=2.3.0
python-dotenv~=1.1.0
opentelemetry-api~=1.20.0
pdf2image~=1.17.0
requests~=2.32.4
sqlalchemy~=2.0.41
pyyaml~=6.0.2
openpyxl~=3.1.5
paho-mqtt~=2.1.0
protobuf~=6.31.1
requests_toolbelt~=1.0.0
playwright~=1.52.0
pydantic~=2.11.7
loguru~=0.7.3
httpx~=0.28.1
utils~=1.0.2
yarl~=1.20.1
pillow~=11.2.1
numpy~=2.3.1
jieba3k~=0.35.1
redis~=6.2.0
beautifulsoup4~=4.13.4
fastapi~=0.115.14
uvicorn~=0.35.0
pyrate-limiter~=3.7.1
aiolimiter~=1.2.1
schedule~=1.2.0
psutil~=7.0.0
"""
        
        requirements_file = self.project_root / "requirements_py311.txt"
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.write(py311_requirements)
        
        print(f"✅ 创建完成: {requirements_file}")
    
    def modernize_type_annotations(self, file_path: Path):
        """现代化类型注解"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 替换导入语句
            import_replacements = [
                (r'from typing import Dict, Any, Optional, Union, List, Tuple', 
                 'from typing import Any'),
                (r'from typing import Dict, Any, Optional, Union, List', 
                 'from typing import Any'),
                (r'from typing import Dict, Any, Optional, Union', 
                 'from typing import Any'),
                (r'from typing import Dict, Any, Optional', 
                 'from typing import Any'),
                (r'from typing import Dict, Any', 
                 'from typing import Any'),
            ]
            
            for pattern, replacement in import_replacements:
                content = re.sub(pattern, replacement, content)
            
            # 替换类型注解
            type_replacements = [
                (r'Dict\[str, Any\]', 'dict[str, Any]'),
                (r'Dict\[str, str\]', 'dict[str, str]'),
                (r'Dict\[str, int\]', 'dict[str, int]'),
                (r'List\[([^\]]+)\]', r'list[\1]'),
                (r'Tuple\[([^\]]+)\]', r'tuple[\1]'),
                (r'Optional\[([^\]]+)\]', r'\1 | None'),
                (r'Union\[([^,]+), None\]', r'\1 | None'),
            ]
            
            for pattern, replacement in type_replacements:
                content = re.sub(pattern, replacement, content)
            
            # 只有内容发生变化时才写入文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
            
            return False
            
        except Exception as e:
            print(f"⚠️ 处理文件失败 {file_path}: {e}")
            return False
    
    def optimize_regex_patterns(self, file_path: Path):
        """优化正则表达式使用海象操作符"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 优化常见的正则匹配模式
            patterns = [
                # match = re.search(...)\n    if match:
                (r'(\s+)(\w+) = re\.search\(([^)]+)\)\s*\n(\s+)if \2:', 
                 r'\1if \2 := re.search(\3):'),
                
                # match = re.match(...)\n    if match:
                (r'(\s+)(\w+) = re\.match\(([^)]+)\)\s*\n(\s+)if \2:', 
                 r'\1if \2 := re.match(\3):'),
                
                # match = re.findall(...)\n    if match:
                (r'(\s+)(\w+) = re\.findall\(([^)]+)\)\s*\n(\s+)if \2:', 
                 r'\1if \2 := re.findall(\3):'),
            ]
            
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            
            # 只有内容发生变化时才写入文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
            
            return False
            
        except Exception as e:
            print(f"⚠️ 优化正则表达式失败 {file_path}: {e}")
            return False
    
    def process_python_files(self):
        """处理所有Python文件"""
        print("🔄 开始现代化Python代码...")
        
        modified_files = []
        total_files = 0
        
        for py_file in self.src_dir.rglob("*.py"):
            total_files += 1
            
            # 现代化类型注解
            type_modified = self.modernize_type_annotations(py_file)
            
            # 优化正则表达式
            regex_modified = self.optimize_regex_patterns(py_file)
            
            if type_modified or regex_modified:
                modified_files.append(py_file)
                changes = []
                if type_modified:
                    changes.append("类型注解")
                if regex_modified:
                    changes.append("正则表达式")
                print(f"  ✅ {py_file.relative_to(self.project_root)}: {', '.join(changes)}")
        
        print(f"📊 处理完成: {len(modified_files)}/{total_files} 个文件被修改")
        return modified_files
    
    def create_windows_compatibility_module(self):
        """创建Windows兼容性模块"""
        print("🪟 创建Windows兼容性模块...")
        
        windows_compat_code = '''# -*- coding: utf-8 -*-
"""
Windows兼容性工具模块
处理Windows特定的文件操作和路径问题
"""
import os
import sys
import time
from pathlib import Path


class WindowsCompatibility:
    """Windows兼容性工具类"""
    
    MAX_PATH_LENGTH = 250 if os.name == 'nt' else 4096
    
    @staticmethod
    def safe_path(path: str | Path) -> Path | None:
        """创建安全的路径，考虑Windows限制"""
        path_obj = Path(path)
        
        if os.name == 'nt' and len(str(path_obj)) > WindowsCompatibility.MAX_PATH_LENGTH:
            # Windows路径过长，返回None
            return None
        
        return path_obj
    
    @staticmethod
    def safe_remove(path: Path, max_retries: int = 3) -> bool:
        """安全删除文件，处理Windows文件锁定"""
        for attempt in range(max_retries):
            try:
                if path.is_file():
                    path.unlink()
                elif path.is_dir():
                    import shutil
                    shutil.rmtree(path)
                return True
            except PermissionError:
                if os.name == 'nt' and attempt < max_retries - 1:
                    time.sleep(0.1 * (attempt + 1))  # 递增等待
                    continue
                return False
            except Exception:
                return False
        
        return False
    
    @staticmethod
    def get_safe_temp_dir() -> Path:
        """获取安全的临时目录"""
        if os.name == 'nt':
            # Windows优先使用用户临时目录
            temp_dirs = [
                Path(os.environ.get('TEMP', '')),
                Path(os.environ.get('LOCALAPPDATA', '')) / "Temp",
                Path("C:/Temp"),
            ]
        else:
            temp_dirs = [
                Path("/tmp"),
                Path(os.path.expanduser("~/.cache")),
            ]
        
        for temp_dir in temp_dirs:
            if temp_dir.exists() and os.access(temp_dir, os.W_OK):
                return temp_dir
        
        # 回退到当前目录
        return Path.cwd() / "temp"


# 全局实例
windows_compat = WindowsCompatibility()
'''
        
        compat_file = self.src_dir / "utils" / "windows_compat.py"
        with open(compat_file, 'w', encoding='utf-8') as f:
            f.write(windows_compat_code)
        
        print(f"✅ 创建完成: {compat_file}")
    
    def create_test_scripts(self):
        """创建测试脚本"""
        print("🧪 创建测试脚本...")
        
        # Python 3.11特性测试
        py311_test = '''#!/usr/bin/env python3
"""Python 3.11特性验证测试"""
import sys
import asyncio
import time

async def test_python311_features():
    """测试Python 3.11特性"""
    print(f"Python版本: {sys.version}")
    
    # 测试海象操作符
    test_string = "test123"
    if pos := test_string.find("123"):
        print("✅ 海象操作符工作正常")
    
    # 测试新的类型注解
    def test_types(data: dict[str, any]) -> str | None:
        return data.get("test")
    
    result = test_types({"test": "success"})
    if result == "success":
        print("✅ 新类型注解工作正常")
    
    # 测试异步性能
    start = time.time()
    tasks = [asyncio.sleep(0.001) for _ in range(100)]
    await asyncio.gather(*tasks)
    end = time.time()
    print(f"✅ 异步性能测试: {end - start:.3f}秒")
    
    print("🎉 Python 3.11特性测试完成")

if __name__ == "__main__":
    asyncio.run(test_python311_features())
'''
        
        test_file = self.project_root / "test_python311_features.py"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(py311_test)
        
        print(f"✅ 创建测试脚本: {test_file}")
    
    def run_upgrade(self):
        """执行完整的升级流程"""
        print("🚀 开始Python 3.11升级流程...")
        print("=" * 50)
        
        try:
            # 1. 创建备份
            self.create_backup()
            
            # 2. 创建新的requirements.txt
            self.create_python311_requirements()
            
            # 3. 现代化代码
            modified_files = self.process_python_files()
            
            # 4. 创建Windows兼容性模块
            self.create_windows_compatibility_module()
            
            # 5. 创建测试脚本
            self.create_test_scripts()
            
            print("\n" + "=" * 50)
            print("🎉 升级完成！")
            print("\n📋 后续步骤:")
            print("1. 安装Python 3.11")
            print("2. 创建新虚拟环境: python3.11 -m venv venv_py311")
            print("3. 激活环境并安装依赖: pip install -r requirements_py311.txt")
            print("4. 运行测试: python test_python311_features.py")
            print("5. 测试主要功能: python src/fast_api.py")
            
            if modified_files:
                print(f"\n📝 共修改了 {len(modified_files)} 个文件")
                print("💾 原始代码已备份到:", self.backup_dir)
            
            return True
            
        except Exception as e:
            print(f"\n❌ 升级过程中发生错误: {e}")
            print("💾 可以从备份恢复:", self.backup_dir)
            return False


def main():
    """主函数"""
    print("SRA项目Python 3.11升级工具")
    print("=" * 40)
    
    # 检查当前目录
    project_root = Path.cwd()
    src_dir = project_root / "src"
    
    if not src_dir.exists():
        print("❌ 错误: 未找到src目录，请在项目根目录运行此脚本")
        return 1
    
    # 检查Python版本
    if sys.version_info < (3, 9):
        print(f"❌ 错误: 当前Python版本 {sys.version} 过低，需要Python 3.9+")
        return 1
    
    print(f"📁 项目目录: {project_root}")
    print(f"🐍 当前Python版本: {sys.version}")
    
    # 确认升级
    response = input("\n是否继续升级到Python 3.11? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ 升级已取消")
        return 0
    
    # 执行升级
    upgrader = Python311Upgrader(project_root)
    success = upgrader.run_upgrade()
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

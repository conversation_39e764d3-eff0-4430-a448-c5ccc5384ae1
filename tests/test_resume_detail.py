#!/usr/bin/env python3
"""
测试获取简历详情功能的脚本
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.browser_manager import init_driver
from src.celery_worker import handle_task
import redis.asyncio as aioredis
from src.conf.config import CONFIG


async def test_get_resume_detail():
    """测试获取简历详情功能"""
    print("开始测试获取简历详情功能...")
    
    # 初始化Redis连接
    redis = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
    
    # 初始化浏览器
    page = await init_driver()
    
    try:
        # 测试任务1：正常的简历详情获取
        print("\n1. 测试正常的简历详情获取...")
        task1 = {
            "id": "test_task_1",
            "action": "get_resume_detail",
            "payload": {
                "url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"  # 示例URL
            },
            "result_channel": "test_result_channel"
        }
        
        result1 = await handle_task(page, task1, redis)
        print(f"任务1结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
        
        # 测试任务2：缺少URL参数
        print("\n2. 测试缺少URL参数...")
        task2 = {
            "id": "test_task_2",
            "action": "get_resume_detail",
            "payload": {},
            "result_channel": "test_result_channel"
        }
        
        result2 = await handle_task(page, task2, redis)
        print(f"任务2结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
        
        # 测试任务3：并发任务（应该返回错误）
        print("\n3. 测试并发任务...")
        
        # 创建一个长时间运行的任务
        async def long_running_task():
            task3a = {
                "id": "test_task_3a",
                "action": "get_resume_detail",
                "payload": {
                    "url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
                },
                "result_channel": "test_result_channel"
            }
            return await handle_task(page, task3a, redis)
        
        # 创建一个并发任务
        async def concurrent_task():
            await asyncio.sleep(0.1)  # 稍微延迟确保第一个任务先开始
            task3b = {
                "id": "test_task_3b",
                "action": "get_resume_detail",
                "payload": {
                    "url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
                },
                "result_channel": "test_result_channel"
            }
            return await handle_task(page, task3b, redis)
        
        # 并发执行两个任务
        results = await asyncio.gather(long_running_task(), concurrent_task(), return_exceptions=True)
        
        print(f"长时间任务结果: {json.dumps(results[0], ensure_ascii=False, indent=2) if not isinstance(results[0], Exception) else str(results[0])}")
        print(f"并发任务结果: {json.dumps(results[1], ensure_ascii=False, indent=2) if not isinstance(results[1], Exception) else str(results[1])}")
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 关闭浏览器和Redis连接
        if page:
            await page.context.close()
        await redis.close()


async def test_api_monitoring():
    """测试API监听功能"""
    print("开始测试API监听功能...")
    
    # 初始化浏览器
    page = await init_driver()
    
    try:
        # 设置请求监听
        captured_data = []
        
        async def handle_response(response):
            if '/wapi/zpboss/h5/resume/share/h5/detail.json' in response.url:
                try:
                    response_data = await response.json()
                    captured_data.append(response_data)
                    print(f"捕获到API响应: {response.url}")
                    print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                except Exception as e:
                    print(f"解析响应失败: {e}")
        
        # 监听响应
        page.on("response", handle_response)
        
        # 访问一个示例页面（需要替换为真实的简历分享页面）
        test_url = "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
        print(f"访问测试URL: {test_url}")
        
        await page.goto(test_url, wait_until="networkidle")
        
        # 等待一段时间看是否有API调用
        await asyncio.sleep(5)
        
        if captured_data:
            print(f"成功捕获到 {len(captured_data)} 个API响应")
            for i, data in enumerate(captured_data):
                print(f"响应 {i+1}: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print("未捕获到目标API响应")
        
        # 移除监听器
        page.remove_listener("response", handle_response)
        
    except Exception as e:
        print(f"API监听测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if page:
            await page.context.close()


if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 测试完整的任务处理功能")
    print("2. 测试API监听功能")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_get_resume_detail())
    elif choice == "2":
        asyncio.run(test_api_monitoring())
    else:
        print("无效选择，默认运行完整测试")
        asyncio.run(test_get_resume_detail())

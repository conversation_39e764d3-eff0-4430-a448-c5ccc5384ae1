#!/usr/bin/env python3
"""
日志重复问题诊断脚本
分析和修复日志重复记录的问题
"""
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class LogDuplicationDiagnoser:
    """日志重复问题诊断器"""
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
    
    def record_issue(self, issue_type: str, description: str, severity: str = "medium"):
        """记录发现的问题"""
        self.issues_found.append({
            "type": issue_type,
            "description": description,
            "severity": severity,
            "timestamp": time.time()
        })
        
        severity_emoji = {
            "low": "⚠️",
            "medium": "🔶", 
            "high": "🔴",
            "critical": "🚨"
        }
        
        emoji = severity_emoji.get(severity, "⚠️")
        print(f"{emoji} [{severity.upper()}] {issue_type}: {description}")
    
    def record_fix(self, fix_description: str):
        """记录应用的修复"""
        self.fixes_applied.append({
            "description": fix_description,
            "timestamp": time.time()
        })
        print(f"✅ 修复: {fix_description}")
    
    def check_logger_initialization(self):
        """检查logger初始化问题"""
        print("\n1. 检查logger初始化...")
        
        try:
            # 检查是否有多次setup_logger调用
            logger_file = Path("src/utils/logger.py")
            if logger_file.exists():
                with open(logger_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查setup_logger调用次数
                setup_calls = content.count("_logger_config.setup_logger()")
                if setup_calls > 1:
                    self.record_issue(
                        "Logger重复初始化",
                        f"发现{setup_calls}次setup_logger调用",
                        "high"
                    )
                else:
                    print("✅ logger初始化正常")
                
                # 检查是否有重复的handler添加
                add_calls = content.count("logger.add(")
                print(f"📊 发现{add_calls}个logger.add()调用")
                
            else:
                self.record_issue(
                    "文件缺失",
                    "logger.py文件不存在",
                    "critical"
                )
                
        except Exception as e:
            self.record_issue(
                "检查异常",
                f"检查logger初始化时发生异常: {e}",
                "medium"
            )
    
    def check_concurrent_processes(self):
        """检查并发进程问题"""
        print("\n2. 检查并发进程...")
        
        try:
            # 检查是否有多个Python进程运行相同的脚本
            import psutil
            
            current_pid = os.getpid()
            python_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and len(cmdline) > 1:
                            script_name = cmdline[1] if len(cmdline) > 1 else ""
                            if any(script in script_name for script in ['celery_worker.py', 'fast_api.py']):
                                python_processes.append({
                                    'pid': proc.info['pid'],
                                    'script': script_name,
                                    'cmdline': ' '.join(cmdline)
                                })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if len(python_processes) > 1:
                self.record_issue(
                    "多进程运行",
                    f"发现{len(python_processes)}个相关Python进程同时运行",
                    "high"
                )
                for proc in python_processes:
                    print(f"  PID {proc['pid']}: {proc['script']}")
            else:
                print("✅ 未发现多进程冲突")
                
        except ImportError:
            print("⚠️ 无法检查进程（缺少psutil库）")
        except Exception as e:
            self.record_issue(
                "进程检查异常",
                f"检查进程时发生异常: {e}",
                "medium"
            )
    
    def check_logger_instances(self):
        """检查logger实例创建"""
        print("\n3. 检查logger实例创建...")
        
        try:
            # 模拟导入logger模块
            from src.utils.logger import get_logger, _logger_instances
            
            # 测试多次获取同一个logger
            logger1 = get_logger("test_module")
            logger2 = get_logger("test_module")
            logger3 = get_logger("test_module")
            
            if logger1 is logger2 is logger3:
                print("✅ logger实例单例模式工作正常")
            else:
                self.record_issue(
                    "Logger实例重复",
                    "相同名称的logger返回了不同的实例",
                    "high"
                )
            
            # 检查缓存状态
            print(f"📊 当前缓存的logger实例数: {len(_logger_instances)}")
            
        except Exception as e:
            self.record_issue(
                "Logger实例检查异常",
                f"检查logger实例时发生异常: {e}",
                "medium"
            )
    
    def check_loguru_handlers(self):
        """检查loguru handlers"""
        print("\n4. 检查loguru handlers...")
        
        try:
            from loguru import logger
            
            # 获取当前的handlers
            handlers = logger._core.handlers
            print(f"📊 当前loguru handlers数量: {len(handlers)}")
            
            # 分析handlers
            file_handlers = 0
            console_handlers = 0
            
            for handler_id, handler in handlers.items():
                sink = handler._sink
                if hasattr(sink, 'write'):
                    if hasattr(sink, 'name'):
                        file_handlers += 1
                        print(f"  文件handler: {sink.name}")
                    else:
                        console_handlers += 1
                        print(f"  控制台handler: {type(sink).__name__}")
            
            print(f"📊 文件handlers: {file_handlers}, 控制台handlers: {console_handlers}")
            
            # 检查是否有重复的文件handlers
            if file_handlers > 2:  # 正常应该有2个：主日志+错误日志
                self.record_issue(
                    "Handler重复",
                    f"发现{file_handlers}个文件handlers，可能存在重复",
                    "medium"
                )
            
        except Exception as e:
            self.record_issue(
                "Handler检查异常",
                f"检查loguru handlers时发生异常: {e}",
                "medium"
            )
    
    def test_log_output(self):
        """测试日志输出"""
        print("\n5. 测试日志输出...")
        
        try:
            from src.utils.logger import get_logger
            
            test_logger = get_logger("test_duplication")
            
            # 记录一条测试日志
            test_message = f"测试日志重复 - {int(time.time())}"
            print(f"发送测试日志: {test_message}")
            
            test_logger.info(test_message)
            
            # 等待日志写入
            time.sleep(0.5)
            
            # 检查日志文件中的重复
            from datetime import datetime
            today = datetime.now().strftime("%Y-%m-%d")
            log_file = Path(f"logs/boss_zhipin_{today}.log")
            
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 计算测试消息出现次数
                occurrences = content.count(test_message)
                print(f"📊 测试消息在日志文件中出现{occurrences}次")
                
                if occurrences > 1:
                    self.record_issue(
                        "日志重复写入",
                        f"测试消息重复出现{occurrences}次",
                        "high"
                    )
                else:
                    print("✅ 日志写入正常，无重复")
            else:
                print("⚠️ 今日日志文件不存在")
                
        except Exception as e:
            self.record_issue(
                "日志测试异常",
                f"测试日志输出时发生异常: {e}",
                "medium"
            )
    
    def check_import_patterns(self):
        """检查导入模式"""
        print("\n6. 检查导入模式...")
        
        try:
            # 检查关键文件的导入方式
            files_to_check = [
                "src/flows/login.py",
                "src/flows/geek_fetch_flow.py",
                "src/celery_worker.py"
            ]
            
            for file_path in files_to_check:
                if Path(file_path).exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查logger导入
                    logger_imports = content.count("from src.utils.logger import")
                    get_logger_calls = content.count("get_logger(")
                    
                    print(f"📊 {file_path}:")
                    print(f"    logger导入: {logger_imports}次")
                    print(f"    get_logger调用: {get_logger_calls}次")
                    
                    if logger_imports > 1:
                        self.record_issue(
                            "重复导入",
                            f"{file_path}中有{logger_imports}次logger导入",
                            "low"
                        )
                        
        except Exception as e:
            self.record_issue(
                "导入检查异常",
                f"检查导入模式时发生异常: {e}",
                "medium"
            )
    
    def suggest_fixes(self):
        """建议修复方案"""
        print("\n" + "="*50)
        print("修复建议")
        print("="*50)
        
        if not self.issues_found:
            print("🎉 未发现明显的日志重复问题！")
            return
        
        # 按严重程度分组问题
        critical_issues = [i for i in self.issues_found if i["severity"] == "critical"]
        high_issues = [i for i in self.issues_found if i["severity"] == "high"]
        medium_issues = [i for i in self.issues_found if i["severity"] == "medium"]
        low_issues = [i for i in self.issues_found if i["severity"] == "low"]
        
        if critical_issues:
            print("\n🚨 严重问题（需要立即修复）:")
            for issue in critical_issues:
                print(f"  - {issue['type']}: {issue['description']}")
        
        if high_issues:
            print("\n🔴 高优先级问题:")
            for issue in high_issues:
                print(f"  - {issue['type']}: {issue['description']}")
        
        if medium_issues:
            print("\n🔶 中等优先级问题:")
            for issue in medium_issues:
                print(f"  - {issue['type']}: {issue['description']}")
        
        if low_issues:
            print("\n⚠️ 低优先级问题:")
            for issue in low_issues:
                print(f"  - {issue['type']}: {issue['description']}")
        
        print("\n💡 通用修复建议:")
        print("1. 确保只运行一个celery_worker.py实例")
        print("2. 重启所有相关进程以清除状态")
        print("3. 检查是否有多个应用同时写入同一日志文件")
        print("4. 验证logger配置是否正确")
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("SRA项目日志重复问题诊断")
        print("=" * 50)
        
        self.check_logger_initialization()
        self.check_concurrent_processes()
        self.check_logger_instances()
        self.check_loguru_handlers()
        self.test_log_output()
        self.check_import_patterns()
        
        self.suggest_fixes()
        
        # 统计结果
        total_issues = len(self.issues_found)
        critical_count = len([i for i in self.issues_found if i["severity"] == "critical"])
        high_count = len([i for i in self.issues_found if i["severity"] == "high"])
        
        print(f"\n📊 诊断结果统计:")
        print(f"总问题数: {total_issues}")
        print(f"严重问题: {critical_count}")
        print(f"高优先级问题: {high_count}")
        
        if total_issues == 0:
            print("\n🎉 诊断完成，未发现明显问题！")
            print("如果仍有日志重复，可能是以下原因：")
            print("1. 多个进程同时运行")
            print("2. 应用重启时的短暂重复")
            print("3. 网络请求重试导致的业务逻辑重复")
        else:
            print(f"\n⚠️ 发现{total_issues}个潜在问题，请按建议进行修复")
        
        return total_issues == 0


def main():
    """主函数"""
    diagnoser = LogDuplicationDiagnoser()
    success = diagnoser.run_diagnosis()
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
简单的日志配置验证脚本
检查logger.py中的配置是否正确
"""
import re
from pathlib import Path

def verify_logger_config():
    """验证logger.py配置"""
    print("验证logger.py日志轮转配置")
    print("=" * 40)
    
    logger_file = Path("src/utils/logger.py")
    
    if not logger_file.exists():
        print("❌ logger.py文件不存在")
        return False
    
    try:
        with open(logger_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置
        checks = [
            {
                "name": "按日期命名的文件模式",
                "pattern": r'boss_zhipin_\{time:YYYY-MM-DD\}\.log',
                "expected": True
            },
            {
                "name": "每日轮转配置",
                "pattern": r'rotation="00:00"',
                "expected": True
            },
            {
                "name": "10天保留期配置",
                "pattern": r'retention="10 days"',
                "expected": True
            },
            {
                "name": "zip压缩配置",
                "pattern": r'compression="zip"',
                "expected": True
            },
            {
                "name": "旧的单文件配置（应该被移除）",
                "pattern": r'boss_zhipin\.log[^_]',
                "expected": False
            },
            {
                "name": "旧的100MB轮转配置（应该被移除）",
                "pattern": r'rotation="100 MB"',
                "expected": False
            },
            {
                "name": "旧的30天保留配置（应该被移除）",
                "pattern": r'retention="30 days"',
                "expected": False
            }
        ]
        
        all_passed = True
        
        for check in checks:
            found = bool(re.search(check["pattern"], content))
            expected = check["expected"]
            
            if found == expected:
                status = "✅ PASS"
            else:
                status = "❌ FAIL"
                all_passed = False
            
            print(f"{status} - {check['name']}: {'找到' if found else '未找到'}")
        
        print("\n配置文件内容预览:")
        print("-" * 40)
        
        # 提取相关配置行
        lines = content.split('\n')
        in_boss_zhipin_config = False
        config_lines = []
        
        for i, line in enumerate(lines):
            if 'boss_zhipin_' in line and '.log' in line:
                in_boss_zhipin_config = True
                # 显示前后几行上下文
                start = max(0, i - 2)
                end = min(len(lines), i + 10)
                config_lines = lines[start:end]
                break
        
        if config_lines:
            for line in config_lines:
                if 'boss_zhipin_' in line or 'rotation=' in line or 'retention=' in line:
                    print(f">>> {line.strip()}")
                else:
                    print(f"    {line.strip()}")
        
        print("-" * 40)
        
        if all_passed:
            print("\n🎉 所有配置检查通过！")
            print("✅ boss_zhipin.log已配置为按日期分割")
            print("✅ 保留期限已设置为10天")
            print("✅ 轮转时间已设置为每日午夜")
            print("✅ 旧配置已正确移除")
        else:
            print("\n❌ 部分配置检查失败，请检查配置")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def show_expected_log_files():
    """显示预期的日志文件格式"""
    from datetime import datetime, timedelta
    
    print("\n预期的日志文件格式:")
    print("-" * 30)
    
    today = datetime.now()
    for i in range(5):
        date = today - timedelta(days=i)
        date_str = date.strftime("%Y-%m-%d")
        if i == 0:
            print(f"boss_zhipin_{date_str}.log     # 今天（当前）")
        elif i == 1:
            print(f"boss_zhipin_{date_str}.log     # 昨天")
        else:
            print(f"boss_zhipin_{date_str}.log.zip # {i}天前（压缩）")
    
    print("...")
    
    # 显示10天前的文件（应该被删除）
    old_date = today - timedelta(days=11)
    old_date_str = old_date.strftime("%Y-%m-%d")
    print(f"boss_zhipin_{old_date_str}.log.zip # 11天前（应被自动删除）")

def main():
    """主函数"""
    success = verify_logger_config()
    show_expected_log_files()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 日志轮转配置验证成功！")
        print("\n下一步:")
        print("1. 重启应用以使新配置生效")
        print("2. 观察logs/目录中的日志文件")
        print("3. 等待午夜时间观察日志轮转")
        return 0
    else:
        print("❌ 日志轮转配置验证失败！")
        print("\n请检查:")
        print("1. src/utils/logger.py文件是否正确修改")
        print("2. 配置参数是否符合要求")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)

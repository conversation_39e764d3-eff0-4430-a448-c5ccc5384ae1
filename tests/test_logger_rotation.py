#!/usr/bin/env python3
"""
日志轮转功能测试脚本
验证boss_zhipin.log按日期分割和保留10天的功能
"""
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger, LoggerConfig

logger = get_logger(__name__)


class LoggerRotationTester:
    """日志轮转测试器"""
    
    def __init__(self):
        self.log_dir = Path("logs")
        self.test_results = []
    
    def record_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        })
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}: {message}")
    
    def test_log_directory_creation(self):
        """测试日志目录创建"""
        test_name = "日志目录创建测试"
        
        try:
            if self.log_dir.exists() and self.log_dir.is_dir():
                self.record_test_result(test_name, True, f"日志目录存在: {self.log_dir}")
            else:
                self.record_test_result(test_name, False, f"日志目录不存在: {self.log_dir}")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def test_daily_log_file_creation(self):
        """测试按日期创建日志文件"""
        test_name = "日期日志文件创建测试"
        
        try:
            # 生成一些测试日志
            logger.info("测试日志轮转功能 - 信息日志")
            logger.warning("测试日志轮转功能 - 警告日志")
            logger.error("测试日志轮转功能 - 错误日志")
            
            # 等待日志写入
            time.sleep(0.5)
            
            # 检查今天的日志文件是否创建
            today = datetime.now().strftime("%Y-%m-%d")
            expected_log_file = self.log_dir / f"boss_zhipin_{today}.log"
            
            if expected_log_file.exists():
                file_size = expected_log_file.stat().st_size
                self.record_test_result(
                    test_name, 
                    True, 
                    f"今日日志文件已创建: {expected_log_file.name} ({file_size} bytes)"
                )
            else:
                self.record_test_result(
                    test_name, 
                    False, 
                    f"今日日志文件未创建: {expected_log_file.name}"
                )
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def test_log_file_content(self):
        """测试日志文件内容"""
        test_name = "日志文件内容测试"
        
        try:
            # 生成带有特殊标识的测试日志
            test_message = f"测试消息_{int(time.time())}"
            logger.info(test_message)
            
            # 等待日志写入
            time.sleep(0.5)
            
            # 检查今天的日志文件内容
            today = datetime.now().strftime("%Y-%m-%d")
            log_file = self.log_dir / f"boss_zhipin_{today}.log"
            
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if test_message in content:
                    self.record_test_result(
                        test_name, 
                        True, 
                        f"日志内容正确写入，包含测试消息"
                    )
                else:
                    self.record_test_result(
                        test_name, 
                        False, 
                        f"日志内容中未找到测试消息"
                    )
            else:
                self.record_test_result(test_name, False, "日志文件不存在")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def test_log_file_naming_pattern(self):
        """测试日志文件命名模式"""
        test_name = "日志文件命名模式测试"
        
        try:
            # 检查日志目录中的文件
            log_files = list(self.log_dir.glob("boss_zhipin_*.log"))
            
            if not log_files:
                self.record_test_result(test_name, False, "未找到任何boss_zhipin日志文件")
                return
            
            # 验证文件名格式
            valid_files = []
            invalid_files = []
            
            for log_file in log_files:
                filename = log_file.name
                # 检查文件名是否符合 boss_zhipin_YYYY-MM-DD.log 格式
                if filename.startswith("boss_zhipin_") and filename.endswith(".log"):
                    date_part = filename[13:-4]  # 提取日期部分
                    try:
                        # 尝试解析日期
                        datetime.strptime(date_part, "%Y-%m-%d")
                        valid_files.append(filename)
                    except ValueError:
                        invalid_files.append(filename)
                else:
                    invalid_files.append(filename)
            
            if invalid_files:
                self.record_test_result(
                    test_name, 
                    False, 
                    f"发现格式错误的文件: {invalid_files}"
                )
            else:
                self.record_test_result(
                    test_name, 
                    True, 
                    f"所有文件命名正确: {valid_files}"
                )
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def test_error_log_file(self):
        """测试错误日志文件"""
        test_name = "错误日志文件测试"
        
        try:
            # 生成错误日志
            error_message = f"测试错误消息_{int(time.time())}"
            logger.error(error_message)
            
            # 等待日志写入
            time.sleep(0.5)
            
            # 检查错误日志文件
            error_log_file = self.log_dir / "error.log"
            
            if error_log_file.exists():
                with open(error_log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if error_message in content:
                    self.record_test_result(
                        test_name, 
                        True, 
                        f"错误日志正确写入error.log"
                    )
                else:
                    self.record_test_result(
                        test_name, 
                        False, 
                        f"错误日志中未找到测试消息"
                    )
            else:
                self.record_test_result(test_name, False, "error.log文件不存在")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def test_logger_configuration(self):
        """测试日志配置"""
        test_name = "日志配置测试"
        
        try:
            config = LoggerConfig()
            
            # 检查配置属性
            if hasattr(config, 'log_dir') and hasattr(config, 'log_format'):
                self.record_test_result(
                    test_name, 
                    True, 
                    f"日志配置正确，日志目录: {config.log_dir}"
                )
            else:
                self.record_test_result(
                    test_name, 
                    False, 
                    "日志配置缺少必要属性"
                )
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def simulate_old_log_files(self):
        """模拟创建旧的日志文件（用于测试保留策略）"""
        test_name = "模拟旧日志文件"
        
        try:
            # 创建一些模拟的旧日志文件
            for days_ago in range(1, 15):  # 创建1-14天前的日志文件
                old_date = datetime.now() - timedelta(days=days_ago)
                old_date_str = old_date.strftime("%Y-%m-%d")
                old_log_file = self.log_dir / f"boss_zhipin_{old_date_str}.log"
                
                # 创建模拟文件
                with open(old_log_file, 'w', encoding='utf-8') as f:
                    f.write(f"模拟日志文件 - {old_date_str}\n")
                    f.write(f"创建时间: {datetime.now()}\n")
            
            # 统计创建的文件
            created_files = list(self.log_dir.glob("boss_zhipin_*.log"))
            self.record_test_result(
                test_name, 
                True, 
                f"创建了 {len(created_files)} 个模拟日志文件"
            )
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def check_retention_policy(self):
        """检查日志保留策略（注意：实际的清理由loguru在运行时执行）"""
        test_name = "日志保留策略检查"
        
        try:
            # 获取所有boss_zhipin日志文件
            log_files = list(self.log_dir.glob("boss_zhipin_*.log"))
            
            if not log_files:
                self.record_test_result(test_name, False, "未找到任何日志文件")
                return
            
            # 分析文件日期
            today = datetime.now().date()
            file_dates = []
            
            for log_file in log_files:
                filename = log_file.name
                if filename.startswith("boss_zhipin_") and filename.endswith(".log"):
                    date_part = filename[13:-4]
                    try:
                        file_date = datetime.strptime(date_part, "%Y-%m-%d").date()
                        days_old = (today - file_date).days
                        file_dates.append((filename, days_old))
                    except ValueError:
                        continue
            
            # 检查是否有超过10天的文件
            old_files = [f for f, days in file_dates if days > 10]
            recent_files = [f for f, days in file_dates if days <= 10]
            
            self.record_test_result(
                test_name, 
                True, 
                f"找到 {len(recent_files)} 个近期文件，{len(old_files)} 个旧文件（注意：loguru会在运行时自动清理旧文件）"
            )
            
            if old_files:
                print(f"    旧文件（应被清理）: {old_files}")
            if recent_files:
                print(f"    近期文件（应保留）: {recent_files}")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("日志轮转功能测试")
        print("=" * 50)
        
        # 运行各项测试
        self.test_log_directory_creation()
        self.test_logger_configuration()
        self.test_daily_log_file_creation()
        self.test_log_file_content()
        self.test_log_file_naming_pattern()
        self.test_error_log_file()
        
        # 模拟旧文件（可选）
        print("\n模拟测试（创建旧日志文件）:")
        self.simulate_old_log_files()
        self.check_retention_policy()
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"\n测试完成统计:")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        # 显示失败的测试
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        return failed_tests == 0
    
    def print_detailed_results(self):
        """打印详细测试结果"""
        print("\n" + "="*60)
        print("日志轮转功能详细测试结果")
        print("="*60)
        
        for result in self.test_results:
            status = "PASS" if result["success"] else "FAIL"
            print(f"[{status}] {result['test_name']}")
            if result["message"]:
                print(f"      {result['message']}")
        
        print("="*60)


def main():
    """主函数"""
    print("SRA项目日志轮转功能测试")
    print("验证boss_zhipin.log按日期分割和保留10天的功能")
    print("=" * 60)
    
    tester = LoggerRotationTester()
    
    try:
        success = tester.run_all_tests()
        
        # 打印详细结果
        tester.print_detailed_results()
        
        if success:
            print("\n🎉 所有测试通过！日志轮转功能正常。")
            print("✅ 日志按日期分割正常")
            print("✅ 日志文件命名正确")
            print("✅ 日志内容写入正常")
            print("✅ 错误日志分离正常")
            print("\n📝 注意事项:")
            print("- loguru会在运行时自动清理超过10天的日志文件")
            print("- 日志文件会在每天午夜自动轮转")
            print("- 旧日志文件会自动压缩为zip格式")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查日志配置。")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

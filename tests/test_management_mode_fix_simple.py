#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试 handle_task 异常处理修复
验证严重异常能够正确触发管理模式的逻辑
"""

import asyncio
import json
import os
import sys
from unittest.mock import AsyncMock, MagicMock, patch

# 模拟异常类
class TaskException(Exception):
    def __init__(self, message, error_code=None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class ErrorCodes:
    LOGIN_REQUIRED = "LOGIN_REQUIRED"
    PAGE_STRUCTURE_CHANGED = "PAGE_STRUCTURE_CHANGED"
    BROWSER_ERROR = "BROWSER_ERROR"
    DATA_PARSING_FAILED = "DATA_PARSING_FAILED"
    TASK_EXECUTION_FAILED = "TASK_EXECUTION_FAILED"
    TASK_CONCURRENT_CONFLICT = "TASK_CONCURRENT_CONFLICT"

# 模拟 handle_task 函数的核心逻辑
async def handle_task_logic(page, task, redis):
    """模拟 handle_task 的核心逻辑"""
    task_id = task.get("id", "N/A")
    action = task.get("action")
    result = None
    should_enter_management_mode = False
    management_exception = None

    try:
        # 模拟任务处理
        if action == "login_required":
            raise TaskException("需要重新登录", ErrorCodes.LOGIN_REQUIRED)
        elif action == "page_structure_changed":
            raise TaskException("页面结构发生变化", ErrorCodes.PAGE_STRUCTURE_CHANGED)
        elif action == "browser_error":
            raise TaskException("浏览器错误", ErrorCodes.BROWSER_ERROR)
        elif action == "general_exception":
            raise Exception("浏览器连接失败")
        elif action == "normal_error":
            raise TaskException("数据解析失败", ErrorCodes.DATA_PARSING_FAILED)
        else:
            result = {"status": "success", "id": task_id}

    except TaskException as e:
        result = {
            "status": "error",
            "id": task_id,
            "message": e.message,
            "error_code": e.error_code
        }
        
        # 所有 TaskException 都需要进入管理模式
        should_enter_management_mode = True
        management_exception = e

    except Exception as e:
        result = {
            "status": "error",
            "id": task_id,
            "message": str(e),
            "error_code": ErrorCodes.TASK_EXECUTION_FAILED
        }
        
        # 所有异常都需要进入管理模式
        should_enter_management_mode = True
        management_exception = e

    # 如果需要进入管理模式，抛出异常
    if should_enter_management_mode and management_exception:
        raise management_exception
    
    return result


async def test_management_mode_logic():
    """测试管理模式触发逻辑"""
    
    # 模拟页面和Redis
    mock_page = AsyncMock()
    mock_redis = AsyncMock()
    
    # 测试场景1：TaskException with LOGIN_REQUIRED
    print("测试场景1：TaskException with LOGIN_REQUIRED")
    task = {"id": "test_001", "action": "login_required"}
    
    try:
        result = await handle_task_logic(mock_page, task, mock_redis)
        print("❌ 错误：应该抛出异常但没有抛出")
    except TaskException as e:
        if e.error_code == ErrorCodes.LOGIN_REQUIRED:
            print("✅ 正确：TaskException with LOGIN_REQUIRED 被正确抛出")
        else:
            print(f"❌ 错误：异常代码不匹配，期望 {ErrorCodes.LOGIN_REQUIRED}，实际 {e.error_code}")
    except Exception as e:
        print(f"❌ 错误：抛出了意外的异常类型：{type(e).__name__}")
    
    # 测试场景2：TaskException with PAGE_STRUCTURE_CHANGED
    print("\n测试场景2：TaskException with PAGE_STRUCTURE_CHANGED")
    task = {"id": "test_002", "action": "page_structure_changed"}
    
    try:
        result = await handle_task_logic(mock_page, task, mock_redis)
        print("❌ 错误：应该抛出异常但没有抛出")
    except TaskException as e:
        if e.error_code == ErrorCodes.PAGE_STRUCTURE_CHANGED:
            print("✅ 正确：TaskException with PAGE_STRUCTURE_CHANGED 被正确抛出")
        else:
            print(f"❌ 错误：异常代码不匹配，期望 {ErrorCodes.PAGE_STRUCTURE_CHANGED}，实际 {e.error_code}")
    
    # 测试场景3：通用 Exception
    print("\n测试场景3：通用 Exception")
    task = {"id": "test_003", "action": "general_exception"}
    
    try:
        result = await handle_task_logic(mock_page, task, mock_redis)
        print("❌ 错误：应该抛出异常但没有抛出")
    except Exception as e:
        if "浏览器连接失败" in str(e):
            print("✅ 正确：通用 Exception 被正确抛出")
        else:
            print(f"❌ 错误：异常不匹配，实际异常：{e}")
    
    # 测试场景4：普通 TaskException（现在也会触发管理模式）
    print("\n测试场景4：普通 TaskException（现在也会触发管理模式）")
    task = {"id": "test_004", "action": "normal_error"}
    
    try:
        result = await handle_task_logic(mock_page, task, mock_redis)
        print("❌ 错误：应该抛出异常但没有抛出")
    except TaskException as e:
        if e.error_code == ErrorCodes.DATA_PARSING_FAILED:
            print("✅ 正确：普通 TaskException 也被正确抛出，触发管理模式")
        else:
            print(f"❌ 错误：异常代码不匹配，期望 {ErrorCodes.DATA_PARSING_FAILED}，实际 {e.error_code}")
    except Exception as e:
        print(f"❌ 错误：普通 TaskException 抛出了意外的异常类型：{type(e).__name__}")
    
    # 测试场景5：正常任务（不应该抛出异常）
    print("\n测试场景5：正常任务（不应该抛出异常）")
    task = {"id": "test_005", "action": "normal"}
    
    try:
        result = await handle_task_logic(mock_page, task, mock_redis)
        if result and result.get("status") == "success":
            print("✅ 正确：正常任务被正确处理，返回成功结果")
        else:
            print("❌ 错误：正常任务处理结果不正确")
    except Exception as e:
        print(f"❌ 错误：正常任务不应该抛出异常，但抛出了：{e}")


async def test_main_loop_integration():
    """测试主循环集成逻辑"""
    print("\n=== 测试主循环集成逻辑 ===")
    
    # 模拟主循环的异常处理
    async def main_loop_logic():
        mock_page = AsyncMock()
        mock_redis = AsyncMock()
        
        # 模拟一个会触发管理模式的异常
        task = {"id": "test_management", "action": "login_required"}
        
        try:
            await handle_task_logic(mock_page, task, mock_redis)
            print("❌ 错误：主循环应该捕获到异常")
        except TaskException as e:
            if e.error_code == ErrorCodes.LOGIN_REQUIRED:
                print("✅ 正确：主循环捕获到 TaskException，可以进入管理模式")
                print("✅ 主循环应该调用 _enter_management_mode 进入管理模式")
            else:
                print(f"❌ 错误：主循环捕获到错误的异常代码：{e.error_code}")
        except Exception as e:
            print(f"❌ 错误：主循环捕获到意外的异常类型：{type(e).__name__}")
    
    await main_loop_logic()


if __name__ == "__main__":
    print("开始测试 handle_task 异常处理修复...")
    
    asyncio.run(test_management_mode_logic())
    asyncio.run(test_main_loop_integration())
    
    print("\n测试完成！")
    print("\n修复说明：")
    print("1. 在 handle_task 中添加了 should_enter_management_mode 和 management_exception 变量")
    print("2. 对于所有 TaskException，都设置标记并重新抛出异常")
    print("3. 对于所有通用 Exception，也设置标记并重新抛出异常")
    print("4. 在 finally 块后检查标记，如果需要则重新抛出异常")
    print("5. 这样主循环就能捕获到所有异常并进入管理模式")
    print("\n修复效果：")
    print("- 所有异常都会被重新抛出，触发主循环的异常处理")
    print("- 主循环能够正确进入管理模式，等待管理员指令")
    print("- 管理员可以对任何异常情况进行干预") 
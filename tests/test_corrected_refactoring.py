#!/usr/bin/env python3
"""
修正后的重构代码验证测试脚本
确保所有原有功能都正确保留
"""
import asyncio
import json
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.task_processor import get_task_processor, TASK_PROCESSORS
from src.core.compatibility import handle_task_legacy, init_driver
from src.utils.logger import get_logger

logger = get_logger(__name__)


class CorrectedRefactoringTester:
    """修正后的重构代码测试器"""
    
    def __init__(self):
        self.page = None
        self.test_results = []
    
    async def setup(self):
        """设置测试环境"""
        try:
            logger.info("初始化测试环境...")
            self.page = await init_driver()
            logger.info("测试环境初始化成功")
            return True
        except Exception as e:
            logger.error(f"测试环境初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        try:
            if self.page:
                await self.page.context.close()
            logger.info("测试环境清理完成")
        except Exception as e:
            logger.error(f"测试环境清理失败: {e}")
    
    def record_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        })
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {message}")
    
    async def test_task_processors_registration(self):
        """测试任务处理器注册"""
        test_name = "任务处理器注册测试"
        
        try:
            # 检查所有必要的任务处理器是否已注册
            expected_processors = [
                "get_resume_detail",
                "login", 
                "jobFilterTrigger"
            ]
            
            missing_processors = []
            for processor_name in expected_processors:
                if processor_name not in TASK_PROCESSORS:
                    missing_processors.append(processor_name)
            
            if missing_processors:
                self.record_test_result(
                    test_name, 
                    False, 
                    f"缺少处理器: {', '.join(missing_processors)}"
                )
                return
            
            # 测试处理器创建
            for processor_name in expected_processors:
                try:
                    processor = get_task_processor(processor_name, self.page)
                    if processor is None:
                        self.record_test_result(
                            test_name, 
                            False, 
                            f"无法创建{processor_name}处理器"
                        )
                        return
                except Exception as e:
                    self.record_test_result(
                        test_name, 
                        False, 
                        f"创建{processor_name}处理器失败: {e}"
                    )
                    return
            
            self.record_test_result(test_name, True, "所有任务处理器注册正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_resume_detail_task(self):
        """测试简历详情获取任务"""
        test_name = "简历详情获取任务测试"
        
        try:
            task = {
                "id": "test_resume_001",
                "action": "get_resume_detail",
                "payload": {
                    "url": "https://www.zhipin.com/web/boss/resume/share?shareId=********"
                },
                "result_channel": "test_channel"
            }
            
            # 测试参数验证
            processor = get_task_processor("get_resume_detail", self.page)
            
            # 测试有效参数
            try:
                await processor.validate_params(task["payload"])
                self.record_test_result(f"{test_name} - 参数验证", True, "有效参数验证通过")
            except Exception as e:
                self.record_test_result(f"{test_name} - 参数验证", False, f"参数验证失败: {e}")
                return
            
            # 测试无效参数
            invalid_payload = {"url": ""}
            try:
                await processor.validate_params(invalid_payload)
                self.record_test_result(f"{test_name} - 无效参数", False, "应该抛出异常但没有")
            except Exception:
                self.record_test_result(f"{test_name} - 无效参数", True, "正确拒绝无效参数")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_login_task(self):
        """测试登录任务"""
        test_name = "登录任务测试"
        
        try:
            task = {
                "id": "test_login_001",
                "action": "login",
                "payload": {
                    "account_name": "test_account",
                    "bn_login_name": "test_bn"
                },
                "result_channel": "test_channel"
            }
            
            # 测试参数验证
            processor = get_task_processor("login", self.page)
            
            # 测试有效参数
            try:
                await processor.validate_params(task["payload"])
                self.record_test_result(f"{test_name} - 参数验证", True, "有效参数验证通过")
            except Exception as e:
                self.record_test_result(f"{test_name} - 参数验证", False, f"参数验证失败: {e}")
                return
            
            # 测试无效参数
            invalid_payload = {}
            try:
                await processor.validate_params(invalid_payload)
                self.record_test_result(f"{test_name} - 无效参数", False, "应该抛出异常但没有")
            except Exception:
                self.record_test_result(f"{test_name} - 无效参数", True, "正确拒绝无效参数")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_job_filter_task(self):
        """测试职位过滤任务"""
        test_name = "职位过滤任务测试"
        
        try:
            task = {
                "id": "test_job_filter_001",
                "action": "jobFilterTrigger",
                "payload": {
                    "account_name": "test_account",
                    "jobId": "123456",
                    "jobName": "Python开发工程师",
                    "batchNo": "batch_001",
                    "filterType": "recommended"
                },
                "result_channel": "test_channel"
            }
            
            # 测试参数验证
            processor = get_task_processor("jobFilterTrigger", self.page)
            
            # 测试有效参数
            try:
                await processor.validate_params(task["payload"])
                self.record_test_result(f"{test_name} - 参数验证", True, "有效参数验证通过")
            except Exception as e:
                self.record_test_result(f"{test_name} - 参数验证", False, f"参数验证失败: {e}")
                return
            
            # 测试无效参数（缺少必要参数）
            invalid_payload = {"account_name": "test"}
            try:
                await processor.validate_params(invalid_payload)
                self.record_test_result(f"{test_name} - 无效参数", False, "应该抛出异常但没有")
            except Exception:
                self.record_test_result(f"{test_name} - 无效参数", True, "正确拒绝无效参数")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_compatibility_adapter(self):
        """测试兼容性适配器"""
        test_name = "兼容性适配器测试"
        
        try:
            # 测试简历详情任务的兼容性处理
            resume_task = {
                "id": "test_compat_resume",
                "action": "get_resume_detail",
                "payload": {
                    "url": "https://www.zhipin.com/web/boss/resume/share?shareId=********"
                }
            }
            
            # 这里只测试适配器是否能正确处理任务结构，不执行实际网络请求
            try:
                # 模拟Redis连接（None也可以，因为这个测试不会实际发布结果）
                result = await handle_task_legacy(self.page, resume_task, None)
                
                if isinstance(result, dict) and "status" in result:
                    self.record_test_result(f"{test_name} - 简历任务", True, "适配器正确处理简历任务")
                else:
                    self.record_test_result(f"{test_name} - 简历任务", False, "适配器返回格式错误")
                    
            except Exception as e:
                # 网络相关的异常是预期的，因为我们没有真实的网络环境
                if "网络" in str(e) or "timeout" in str(e).lower() or "navigation" in str(e).lower():
                    self.record_test_result(f"{test_name} - 简历任务", True, "适配器正确处理（网络异常预期）")
                else:
                    self.record_test_result(f"{test_name} - 简历任务", False, f"适配器异常: {e}")
            
            # 测试登录任务的兼容性处理
            login_task = {
                "id": "test_compat_login",
                "action": "login",
                "payload": {
                    "account_name": "test_account"
                }
            }
            
            try:
                result = await handle_task_legacy(self.page, login_task, None)
                
                if isinstance(result, dict) and "status" in result:
                    self.record_test_result(f"{test_name} - 登录任务", True, "适配器正确处理登录任务")
                else:
                    self.record_test_result(f"{test_name} - 登录任务", False, "适配器返回格式错误")
                    
            except Exception as e:
                # 登录相关的异常也是预期的
                if any(keyword in str(e).lower() for keyword in ["login", "cookie", "navigation", "timeout"]):
                    self.record_test_result(f"{test_name} - 登录任务", True, "适配器正确处理（登录异常预期）")
                else:
                    self.record_test_result(f"{test_name} - 登录任务", False, f"适配器异常: {e}")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_original_functions_availability(self):
        """测试原有函数的可用性"""
        test_name = "原有函数可用性测试"
        
        try:
            # 测试init_driver函数
            if not callable(init_driver):
                self.record_test_result(test_name, False, "init_driver函数不可用")
                return
            
            # 测试handle_task_legacy函数
            if not callable(handle_task_legacy):
                self.record_test_result(test_name, False, "handle_task_legacy函数不可用")
                return
            
            # 测试原有的flows模块是否可导入
            try:
                from src.flows.login import login
                from src.flows.geek_fetch_flow import fetch_recommended_geeks
                
                if not callable(login):
                    self.record_test_result(test_name, False, "login函数不可用")
                    return
                
                if not callable(fetch_recommended_geeks):
                    self.record_test_result(test_name, False, "fetch_recommended_geeks函数不可用")
                    return
                
            except ImportError as e:
                self.record_test_result(test_name, False, f"无法导入原有模块: {e}")
                return
            
            self.record_test_result(test_name, True, "所有原有函数都可用")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行修正后的重构代码验证测试...")
        
        if not await self.setup():
            logger.error("测试环境设置失败，终止测试")
            return False
        
        try:
            # 运行各项测试
            await self.test_task_processors_registration()
            await self.test_resume_detail_task()
            await self.test_login_task()
            await self.test_job_filter_task()
            await self.test_compatibility_adapter()
            await self.test_original_functions_availability()
            
            # 统计测试结果
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results if result["success"])
            failed_tests = total_tests - passed_tests
            
            logger.info(f"\n测试完成统计:")
            logger.info(f"总测试数: {total_tests}")
            logger.info(f"通过: {passed_tests}")
            logger.info(f"失败: {failed_tests}")
            logger.info(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
            
            # 显示失败的测试
            if failed_tests > 0:
                logger.error("\n失败的测试:")
                for result in self.test_results:
                    if not result["success"]:
                        logger.error(f"  - {result['test_name']}: {result['message']}")
            
            return failed_tests == 0
            
        finally:
            await self.cleanup()
    
    def print_detailed_results(self):
        """打印详细测试结果"""
        print("\n" + "="*60)
        print("修正后重构代码详细测试结果")
        print("="*60)
        
        for result in self.test_results:
            status = "PASS" if result["success"] else "FAIL"
            print(f"[{status}] {result['test_name']}")
            if result["message"]:
                print(f"      {result['message']}")
        
        print("="*60)


async def main():
    """主函数"""
    print("SRA项目修正后重构代码验证测试")
    print("="*50)
    
    tester = CorrectedRefactoringTester()
    
    try:
        success = await tester.run_all_tests()
        
        # 打印详细结果
        tester.print_detailed_results()
        
        if success:
            print("\n🎉 所有测试通过！修正后的重构代码功能正常。")
            print("✅ 原有功能完整保留")
            print("✅ 新架构正确实现")
            print("✅ 兼容性适配器工作正常")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查修正后的代码。")
            return 1
            
    except Exception as e:
        logger.error(f"测试执行异常: {e}", exc_info=True)
        print(f"\n💥 测试执行异常: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
测试tracing功能的脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.browser_manager import init_driver
from src.utils.tracing_manager import start_trace, stop_trace, get_tracing_manager


async def test_tracing():
    """测试tracing功能"""
    print("开始测试tracing功能...")
    
    # 初始化浏览器
    page = await init_driver()
    
    try:
        # 测试基本的tracing功能
        print("1. 测试启动tracing...")
        await start_trace(page, "test_operation", "test_user")
        
        # 执行一些基本操作
        print("2. 执行测试操作...")
        await page.goto("https://www.baidu.com")
        await page.wait_for_load_state("networkidle")
        
        # 停止tracing
        print("3. 停止tracing...")
        trace_file = await stop_trace(page)
        
        if trace_file:
            print(f"✅ Tracing测试成功！文件已保存: {trace_file}")
        else:
            print("❌ Tracing测试失败：未能保存文件")
        
        # 测试tracing管理器
        print("4. 测试tracing管理器...")
        tracing_manager = get_tracing_manager(page)
        if tracing_manager:
            print("✅ Tracing管理器初始化成功")
            
            # 测试重启tracing
            await tracing_manager.restart_trace("test_restart", "test_user")
            await page.goto("https://www.google.com")
            await page.wait_for_load_state("networkidle")
            
            restart_file = await tracing_manager.stop_current_trace()
            if restart_file:
                print(f"✅ Tracing重启测试成功！文件已保存: {restart_file}")
            else:
                print("❌ Tracing重启测试失败")
        else:
            print("❌ Tracing管理器初始化失败")
        
        print("\n测试完成！")
        print("可以使用以下命令查看trace文件：")
        if trace_file:
            print(f"npx playwright show-trace {trace_file}")
        if restart_file:
            print(f"npx playwright show-trace {restart_file}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 关闭浏览器
        if page:
            await page.context.close()


if __name__ == "__main__":
    asyncio.run(test_tracing())

#!/usr/bin/env python3
"""
企业微信机器人功能测试脚本
验证消息发送、截图上传等功能
"""
import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.mailer import WeChatWorkBot, get_wechat_bot, send_monitoring_alert, send_task_notification
from src.core.browser_manager import init_driver
from src.utils.logger import get_logger

logger = get_logger(__name__)


class WeChatBotTester:
    """企业微信机器人测试器"""
    
    def __init__(self):
        self.bot = get_wechat_bot()
        self.page = None
        self.test_results = []
    
    async def setup(self):
        """设置测试环境"""
        try:
            logger.info("初始化测试环境...")
            self.page = await init_driver()
            logger.info("测试环境初始化成功")
            return True
        except Exception as e:
            logger.error(f"测试环境初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        try:
            if self.page:
                await self.page.context.close()
            logger.info("测试环境清理完成")
        except Exception as e:
            logger.error(f"测试环境清理失败: {e}")
    
    def record_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        })
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {message}")
    
    async def test_text_message(self):
        """测试文本消息发送"""
        test_name = "文本消息发送测试"
        
        try:
            message = f"SRA系统测试消息 - {time.strftime('%Y-%m-%d %H:%M:%S')}"
            success = await self.bot.send_text_message(message)
            
            if success:
                self.record_test_result(test_name, True, "文本消息发送成功")
            else:
                self.record_test_result(test_name, False, "文本消息发送失败")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_markdown_message(self):
        """测试Markdown消息发送"""
        test_name = "Markdown消息发送测试"
        
        try:
            markdown_content = f"""## SRA系统测试报告
            
**测试时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}
**测试类型**: Markdown消息测试
**状态**: 测试中

### 测试内容
- 文本格式化
- 列表显示
- **粗体文本**
- `代码块`

> 这是一条测试消息，用于验证Markdown格式是否正确显示。
            """
            
            success = await self.bot.send_markdown_message(markdown_content)
            
            if success:
                self.record_test_result(test_name, True, "Markdown消息发送成功")
            else:
                self.record_test_result(test_name, False, "Markdown消息发送失败")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_monitoring_alert(self):
        """测试监控告警消息"""
        test_name = "监控告警消息测试"
        
        try:
            title = "SRA系统测试告警"
            error_details = """这是一个测试告警消息。
            
模拟的错误信息：
- 连接超时
- 数据解析失败
- 网络异常

堆栈信息：
  File "test.py", line 100, in test_function
    raise Exception("测试异常")
Exception: 测试异常"""
            
            additional_info = {
                "服务器": "test-server-01",
                "进程ID": "12345",
                "内存使用": "256MB",
                "CPU使用率": "45%"
            }
            
            success = await self.bot.send_monitoring_alert(
                title=title,
                error_details=error_details,
                page=self.page,
                severity="warning",
                additional_info=additional_info
            )
            
            if success:
                self.record_test_result(test_name, True, "监控告警消息发送成功（包含截图）")
            else:
                self.record_test_result(test_name, False, "监控告警消息发送失败")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_task_notification(self):
        """测试任务通知消息"""
        test_name = "任务通知消息测试"
        
        try:
            # 测试成功任务通知
            success1 = await self.bot.send_task_notification(
                task_id="test_task_001",
                task_type="get_resume_detail",
                status="success",
                message="简历详情获取成功",
                duration=3.45,
                account_name="test_account"
            )
            
            await asyncio.sleep(1)  # 避免消息发送过快
            
            # 测试失败任务通知
            success2 = await self.bot.send_task_notification(
                task_id="test_task_002",
                task_type="login",
                status="error",
                message="登录失败：网络连接超时",
                duration=10.0,
                account_name="test_account"
            )
            
            if success1 and success2:
                self.record_test_result(test_name, True, "任务通知消息发送成功")
            else:
                self.record_test_result(test_name, False, "部分任务通知消息发送失败")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_screenshot_capture(self):
        """测试截图功能"""
        test_name = "截图功能测试"

        try:
            if not self.page:
                self.record_test_result(test_name, False, "页面对象未初始化")
                return

            # 检查页面状态
            if self.page.is_closed():
                self.record_test_result(test_name, False, "页面已关闭")
                return

            # 导航到一个测试页面
            logger.info("导航到测试页面...")
            await self.page.goto("https://www.baidu.com", wait_until="networkidle")
            await asyncio.sleep(2)  # 等待页面完全加载

            # 检查页面是否加载成功
            page_title = await self.page.title()
            logger.info(f"页面标题: {page_title}")

            # 捕获截图
            logger.info("开始捕获截图...")
            screenshot_data = await self.bot._capture_screenshot(self.page)

            if screenshot_data and len(screenshot_data) > 0:
                logger.info(f"截图捕获成功，大小: {len(screenshot_data)} bytes")

                # 尝试发送截图
                logger.info("发送截图到企业微信...")
                success = await self.bot.send_image_message(screenshot_data)

                if success:
                    self.record_test_result(test_name, True, f"截图捕获并发送成功，大小: {len(screenshot_data)} bytes")
                else:
                    self.record_test_result(test_name, False, "截图发送失败")
            else:
                self.record_test_result(test_name, False, "截图捕获失败，返回数据为空")

        except Exception as e:
            logger.error(f"截图测试异常: {e}", exc_info=True)
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_compatibility_functions(self):
        """测试兼容性函数"""
        test_name = "兼容性函数测试"
        
        try:
            # 测试send_monitoring_alert兼容性函数
            success1 = await send_monitoring_alert(
                title="兼容性测试告警",
                error_details="这是通过兼容性函数发送的测试告警",
                page=self.page,
                severity="info"
            )
            
            await asyncio.sleep(1)
            
            # 测试send_task_notification兼容性函数
            success2 = await send_task_notification(
                task_id="compat_test_001",
                task_type="compatibility_test",
                status="success",
                message="兼容性函数测试成功"
            )
            
            if success1 and success2:
                self.record_test_result(test_name, True, "兼容性函数工作正常")
            else:
                self.record_test_result(test_name, False, "部分兼容性函数失败")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行企业微信机器人功能测试...")
        
        if not await self.setup():
            logger.error("测试环境设置失败，终止测试")
            return False
        
        try:
            # 发送测试开始通知
            await self.bot.send_text_message("🚀 SRA企业微信机器人功能测试开始")
            await asyncio.sleep(1)
            
            # 运行各项测试
            await self.test_text_message()
            await asyncio.sleep(2)
            
            await self.test_markdown_message()
            await asyncio.sleep(2)
            
            await self.test_task_notification()
            await asyncio.sleep(2)
            
            await self.test_screenshot_capture()
            await asyncio.sleep(2)
            
            await self.test_monitoring_alert()
            await asyncio.sleep(2)
            
            await self.test_compatibility_functions()
            await asyncio.sleep(1)
            
            # 统计测试结果
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results if result["success"])
            failed_tests = total_tests - passed_tests
            
            # 发送测试结果摘要
            summary_message = f"""## 🧪 SRA企业微信机器人测试完成

**测试统计**:
- 总测试数: {total_tests}
- 通过: {passed_tests}
- 失败: {failed_tests}
- 成功率: {(passed_tests/total_tests)*100:.1f}%

**测试状态**: {'✅ 全部通过' if failed_tests == 0 else '❌ 部分失败'}
            """
            
            await self.bot.send_markdown_message(summary_message)
            
            logger.info(f"\n测试完成统计:")
            logger.info(f"总测试数: {total_tests}")
            logger.info(f"通过: {passed_tests}")
            logger.info(f"失败: {failed_tests}")
            logger.info(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
            
            # 显示失败的测试
            if failed_tests > 0:
                logger.error("\n失败的测试:")
                for result in self.test_results:
                    if not result["success"]:
                        logger.error(f"  - {result['test_name']}: {result['message']}")
            
            return failed_tests == 0
            
        finally:
            await self.cleanup()
    
    def print_detailed_results(self):
        """打印详细测试结果"""
        print("\n" + "="*60)
        print("企业微信机器人功能测试详细结果")
        print("="*60)
        
        for result in self.test_results:
            status = "PASS" if result["success"] else "FAIL"
            print(f"[{status}] {result['test_name']}")
            if result["message"]:
                print(f"      {result['message']}")
        
        print("="*60)


async def main():
    """主函数"""
    print("SRA企业微信机器人功能测试")
    print("="*50)
    
    tester = WeChatBotTester()
    
    try:
        success = await tester.run_all_tests()
        
        # 打印详细结果
        tester.print_detailed_results()
        
        if success:
            print("\n🎉 所有测试通过！企业微信机器人功能正常。")
            print("✅ 文本消息发送正常")
            print("✅ Markdown消息发送正常")
            print("✅ 截图功能正常")
            print("✅ 监控告警功能正常")
            print("✅ 任务通知功能正常")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查企业微信机器人配置。")
            return 1
            
    except Exception as e:
        logger.error(f"测试执行异常: {e}", exc_info=True)
        print(f"\n💥 测试执行异常: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

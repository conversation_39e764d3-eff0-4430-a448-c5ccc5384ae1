#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改造后的action code功能
"""

import sys
import os
import unittest

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.flows.geek_filter import (
    processResult, 
    ACTION_NONE, ACTION_HI, ACTION_HI_OVER_STOP, ACTION_NEXT, 
    ACTION_FAV_SAVE, ACTION_FAV_SAVE_STOP, ACTION_BATCH_HI, ACTION_BATCH_HI_OVER_STOP
)


class TestActionCodeRefactored(unittest.TestCase):
    """测试改造后的action code功能"""
    
    def setUp(self):
        """测试前准备"""
        pass
        
    def test_action_code_hi_continue(self):
        """测试打招呼且继续的情况"""
        print("\n=== 测试打招呼且继续 ===")
        
        result = processResult(isContinue="1", status="1", process=5, matchprocess=3)
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertEqual(actionCode, ACTION_HI)  # 打招呼
        self.assertFalse(success)  # 未完成
        
    def test_action_code_hi_stop(self):
        """测试打招呼且停止的情况"""
        print("\n=== 测试打招呼且停止 ===")
        
        result = processResult(isContinue="0", status="1", process=5, matchprocess=3)
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "2")  # 结束
        self.assertEqual(actionCode, ACTION_HI_OVER_STOP)  # 打招呼结束
        self.assertTrue(success)  # 完成
        
    def test_action_code_no_hi_continue(self):
        """测试不打招呼且继续的情况"""
        print("\n=== 测试不打招呼且继续 ===")
        
        result = processResult(isContinue="1", status="0", process=5, matchprocess=3)
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertEqual(actionCode, ACTION_NEXT)  # 下一个
        self.assertFalse(success)  # 未完成
        
    def test_action_code_no_hi_stop(self):
        """测试不打招呼且停止的情况"""
        print("\n=== 测试不打招呼且停止 ===")
        
        result = processResult(isContinue="0", status="0", process=5, matchprocess=3)
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续（默认）
        self.assertEqual(actionCode, ACTION_NONE)  # 无操作
        self.assertTrue(success)  # 完成
        
    def test_action_code_fav_continue(self):
        """测试收藏且继续的情况"""
        print("\n=== 测试收藏且继续 ===")
        
        result = processResult(isContinue="1", status="2", process=5, matchprocess=3)
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertEqual(actionCode, ACTION_FAV_SAVE)  # 收藏
        self.assertFalse(success)  # 未完成
        
    def test_action_code_fav_stop(self):
        """测试收藏且停止的情况"""
        print("\n=== 测试收藏且停止 ===")
        
        result = processResult(isContinue="0", status="2", process=5, matchprocess=3)
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "2")  # 结束
        self.assertEqual(actionCode, ACTION_FAV_SAVE_STOP)  # 收藏结束
        self.assertTrue(success)  # 完成
        
    def test_action_code_batch_hi_continue(self):
        """测试批量打招呼且继续的情况"""
        print("\n=== 测试批量打招呼且继续 ===")
        
        batch_ids = ["candidate1", "candidate2", "candidate3"]
        result = processResult(isContinue="1", status="3", process=5, matchprocess=3, 
                             batchHiCandicateIds=batch_ids)
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertEqual(actionCode, ACTION_BATCH_HI)  # 批量打招呼
        self.assertFalse(success)  # 未完成
        
    def test_action_code_batch_hi_stop(self):
        """测试批量打招呼且停止的情况"""
        print("\n=== 测试批量打招呼且停止 ===")
        
        batch_ids = ["candidate1", "candidate2", "candidate3"]
        result = processResult(isContinue="0", status="3", process=5, matchprocess=3, 
                             batchHiCandicateIds=batch_ids)
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "2")  # 结束
        self.assertEqual(actionCode, ACTION_BATCH_HI_OVER_STOP)  # 批量打招呼结束
        self.assertTrue(success)  # 完成
        
    def test_action_code_batch_hi_empty(self):
        """测试批量打招呼但ID列表为空的情况"""
        print("\n=== 测试批量打招呼但ID列表为空 ===")
        
        result = processResult(isContinue="1", status="3", process=5, matchprocess=3, 
                             batchHiCandicateIds=[])
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertEqual(actionCode, ACTION_BATCH_HI)  # 批量打招呼
        self.assertFalse(success)  # 未完成
        
    def test_action_code_default_params(self):
        """测试默认参数的情况"""
        print("\n=== 测试默认参数 ===")
        
        result = processResult()
        runStatus, actionCode, success = result
        
        print(f"结果: runStatus={runStatus}, actionCode={actionCode}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续（默认）
        self.assertEqual(actionCode, ACTION_NONE)  # 无操作
        self.assertTrue(success)  # 完成
        
    def test_action_code_constants(self):
        """测试action code常量定义"""
        print("\n=== 测试action code常量定义 ===")
        
        # 验证所有常量都已定义
        self.assertEqual(ACTION_NONE, "NONE")
        self.assertEqual(ACTION_HI, "HI")
        self.assertEqual(ACTION_HI_OVER_STOP, "HI_OVER_STOP")
        self.assertEqual(ACTION_NEXT, "NEXT")
        self.assertEqual(ACTION_FAV_SAVE, "FAV_SAVE")
        self.assertEqual(ACTION_FAV_SAVE_STOP, "FAV_SAVE_STOP")
        self.assertEqual(ACTION_BATCH_HI, "BATCH_HI")
        self.assertEqual(ACTION_BATCH_HI_OVER_STOP, "BATCH_HI_OVER_STOP")
        
        print("✓ 所有action code常量定义正确")


def run_all_tests():
    """运行所有测试"""
    print("开始测试改造后的action code功能...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试用例
    test_cases = [
        TestActionCodeRefactored("test_action_code_hi_continue"),
        TestActionCodeRefactored("test_action_code_hi_stop"),
        TestActionCodeRefactored("test_action_code_no_hi_continue"),
        TestActionCodeRefactored("test_action_code_no_hi_stop"),
        TestActionCodeRefactored("test_action_code_fav_continue"),
        TestActionCodeRefactored("test_action_code_fav_stop"),
        TestActionCodeRefactored("test_action_code_batch_hi_continue"),
        TestActionCodeRefactored("test_action_code_batch_hi_stop"),
        TestActionCodeRefactored("test_action_code_batch_hi_empty"),
        TestActionCodeRefactored("test_action_code_default_params"),
        TestActionCodeRefactored("test_action_code_constants"),
    ]
    
    for test_case in test_cases:
        test_suite.addTest(test_case)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n测试结果: 运行 {result.testsRun} 个测试")
    print(f"失败: {len(result.failures)} 个")
    print(f"错误: {len(result.errors)} 个")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_all_tests()
    if success:
        print("\n✓ 所有测试通过！")
    else:
        print("\n✗ 部分测试失败！") 
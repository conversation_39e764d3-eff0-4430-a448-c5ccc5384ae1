#!/usr/bin/env python3
"""
日志重复问题修复脚本
自动修复导致日志重复记录的问题
"""
import os
import sys
import time
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class LogDuplicationFixer:
    """日志重复问题修复器"""
    
    def __init__(self):
        self.fixes_applied = []
    
    def record_fix(self, fix_description: str):
        """记录应用的修复"""
        self.fixes_applied.append({
            "description": fix_description,
            "timestamp": time.time()
        })
        print(f"✅ {fix_description}")
    
    def kill_duplicate_processes(self):
        """终止重复的进程"""
        print("1. 检查并终止重复进程...")
        
        try:
            import psutil
            
            # 查找相关的Python进程
            target_scripts = ['celery_worker.py', 'fast_api.py']
            processes_to_kill = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and len(cmdline) > 1:
                            script_name = cmdline[1] if len(cmdline) > 1 else ""
                            if any(script in script_name for script in target_scripts):
                                processes_to_kill.append({
                                    'pid': proc.info['pid'],
                                    'script': script_name,
                                    'process': proc
                                })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if len(processes_to_kill) > 1:
                print(f"发现{len(processes_to_kill)}个相关进程，将终止重复进程...")
                
                # 保留最新的进程，终止其他的
                processes_to_kill.sort(key=lambda x: x['pid'])
                
                for i, proc_info in enumerate(processes_to_kill[:-1]):  # 保留最后一个
                    try:
                        proc_info['process'].terminate()
                        print(f"  终止进程 PID {proc_info['pid']}: {proc_info['script']}")
                        time.sleep(1)
                        
                        # 如果进程还没有终止，强制杀死
                        if proc_info['process'].is_running():
                            proc_info['process'].kill()
                            print(f"  强制杀死进程 PID {proc_info['pid']}")
                            
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
                self.record_fix(f"终止了{len(processes_to_kill)-1}个重复进程")
            else:
                print("✅ 未发现重复进程")
                
        except ImportError:
            print("⚠️ 无法检查进程（缺少psutil库），请手动检查")
            print("手动检查命令: ps aux | grep python")
        except Exception as e:
            print(f"❌ 检查进程时发生异常: {e}")
    
    def fix_logger_configuration(self):
        """修复logger配置"""
        print("\n2. 检查并修复logger配置...")
        
        logger_file = Path("src/utils/logger.py")
        if not logger_file.exists():
            print("❌ logger.py文件不存在")
            return
        
        try:
            with open(logger_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经有单例模式修复
            if "_logger_instances = {}" in content and "if name not in _logger_instances:" in content:
                print("✅ logger单例模式已经配置")
            else:
                print("⚠️ logger单例模式配置可能有问题")
            
            # 检查setup_logger调用次数
            setup_calls = content.count("_logger_config.setup_logger()")
            if setup_calls == 1:
                print("✅ logger初始化配置正常")
            else:
                print(f"⚠️ 发现{setup_calls}次setup_logger调用")
                
        except Exception as e:
            print(f"❌ 检查logger配置时发生异常: {e}")
    
    def clear_logger_cache(self):
        """清理logger缓存"""
        print("\n3. 清理logger缓存...")
        
        try:
            # 尝试清理可能的logger缓存
            from src.utils.logger import _logger_instances
            
            original_count = len(_logger_instances)
            _logger_instances.clear()
            
            if original_count > 0:
                self.record_fix(f"清理了{original_count}个缓存的logger实例")
            else:
                print("✅ logger缓存为空")
                
        except Exception as e:
            print(f"⚠️ 清理logger缓存时发生异常: {e}")
    
    def reset_loguru_handlers(self):
        """重置loguru handlers"""
        print("\n4. 重置loguru handlers...")
        
        try:
            from loguru import logger
            
            # 获取当前handlers数量
            original_count = len(logger._core.handlers)
            print(f"当前handlers数量: {original_count}")
            
            # 移除所有handlers
            logger.remove()
            print("✅ 已移除所有loguru handlers")
            
            # 重新设置logger配置
            from src.utils.logger import LoggerConfig
            config = LoggerConfig()
            config.setup_logger()
            
            new_count = len(logger._core.handlers)
            print(f"重新配置后handlers数量: {new_count}")
            
            self.record_fix(f"重置loguru handlers: {original_count} -> {new_count}")
            
        except Exception as e:
            print(f"❌ 重置loguru handlers时发生异常: {e}")
    
    def test_log_duplication(self):
        """测试日志重复情况"""
        print("\n5. 测试日志重复情况...")
        
        try:
            from src.utils.logger import get_logger
            
            test_logger = get_logger("duplication_test")
            
            # 发送测试消息
            test_message = f"重复测试消息 - {int(time.time())}"
            print(f"发送测试消息: {test_message}")
            
            test_logger.info(test_message)
            
            # 等待日志写入
            time.sleep(1)
            
            # 检查日志文件
            from datetime import datetime
            today = datetime.now().strftime("%Y-%m-%d")
            log_file = Path(f"logs/boss_zhipin_{today}.log")
            
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                occurrences = content.count(test_message)
                print(f"测试消息在日志中出现{occurrences}次")
                
                if occurrences == 1:
                    print("✅ 日志重复问题已修复")
                    return True
                else:
                    print(f"❌ 日志仍然重复{occurrences}次")
                    return False
            else:
                print("⚠️ 日志文件不存在，无法验证")
                return False
                
        except Exception as e:
            print(f"❌ 测试日志时发生异常: {e}")
            return False
    
    def create_process_check_script(self):
        """创建进程检查脚本"""
        print("\n6. 创建进程检查脚本...")
        
        script_content = '''#!/bin/bash
# 检查SRA相关进程的脚本

echo "=== SRA进程检查 ==="
echo "当前时间: $(date)"
echo

echo "Python进程:"
ps aux | grep python | grep -E "(celery_worker|fast_api)" | grep -v grep

echo
echo "端口占用:"
netstat -tlnp 2>/dev/null | grep :8000 || echo "端口8000未被占用"

echo
echo "日志文件:"
ls -la logs/boss_zhipin_*.log 2>/dev/null || echo "未找到日志文件"

echo
echo "最近的日志条目:"
tail -5 logs/boss_zhipin_$(date +%Y-%m-%d).log 2>/dev/null || echo "无法读取今日日志"
'''
        
        script_file = Path("check_processes.sh")
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(script_file, 0o755)
        
        self.record_fix("创建了进程检查脚本 check_processes.sh")
    
    def provide_monitoring_commands(self):
        """提供监控命令"""
        print("\n7. 监控命令...")
        
        commands = [
            "# 实时查看日志",
            "tail -f logs/boss_zhipin_$(date +%Y-%m-%d).log",
            "",
            "# 检查进程",
            "ps aux | grep python | grep -E '(celery_worker|fast_api)'",
            "",
            "# 检查端口",
            "netstat -tlnp | grep :8000",
            "",
            "# 统计日志重复",
            "grep '登录数据已保存到本地文件' logs/boss_zhipin_$(date +%Y-%m-%d).log | wc -l",
            "",
            "# 查看最近的日志",
            "tail -20 logs/boss_zhipin_$(date +%Y-%m-%d).log"
        ]
        
        print("常用监控命令:")
        for cmd in commands:
            if cmd.startswith("#"):
                print(f"\n{cmd}")
            elif cmd:
                print(f"  {cmd}")
    
    def run_fix(self):
        """运行完整修复流程"""
        print("SRA项目日志重复问题修复")
        print("=" * 50)
        
        # 执行修复步骤
        self.kill_duplicate_processes()
        self.fix_logger_configuration()
        self.clear_logger_cache()
        self.reset_loguru_handlers()
        
        # 测试修复效果
        success = self.test_log_duplication()
        
        # 创建辅助工具
        self.create_process_check_script()
        self.provide_monitoring_commands()
        
        # 总结
        print("\n" + "="*50)
        print("修复总结")
        print("="*50)
        
        if self.fixes_applied:
            print("应用的修复:")
            for fix in self.fixes_applied:
                print(f"  ✅ {fix['description']}")
        
        if success:
            print("\n🎉 日志重复问题修复成功！")
            print("\n建议:")
            print("1. 重启应用以确保修复生效")
            print("2. 使用 ./check_processes.sh 监控进程状态")
            print("3. 观察日志文件确认不再重复")
        else:
            print("\n⚠️ 修复可能不完整，建议:")
            print("1. 手动检查是否有多个进程运行")
            print("2. 重启所有相关服务")
            print("3. 检查应用代码中是否有重复的日志调用")
        
        print("\n下一步操作:")
        print("1. 重启celery_worker: python src/celery_worker.py")
        print("2. 重启fast_api: python src/fast_api.py")
        print("3. 监控日志: tail -f logs/boss_zhipin_$(date +%Y-%m-%d).log")
        
        return success


def main():
    """主函数"""
    print("是否要执行日志重复问题修复? (y/N): ", end="")
    response = input().strip().lower()
    
    if response not in ['y', 'yes']:
        print("修复已取消")
        return 0
    
    fixer = LogDuplicationFixer()
    success = fixer.run_fix()
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

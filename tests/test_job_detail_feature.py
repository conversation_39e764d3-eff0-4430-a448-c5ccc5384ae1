#!/usr/bin/env python3
"""
职位详情获取功能测试脚本
验证task_processor.py中新增的职位详情获取逻辑
"""
import asyncio
import json
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger

logger = get_logger(__name__)


class JobDetailFeatureTester:
    """职位详情功能测试器"""
    
    def __init__(self):
        self.test_results = []
    
    def record_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        })
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}: {message}")
    
    def test_code_structure(self):
        """测试代码结构是否正确"""
        test_name = "代码结构验证"
        
        try:
            # 检查task_processor.py文件
            processor_file = Path("src/core/task_processor.py")
            if not processor_file.exists():
                self.record_test_result(test_name, False, "task_processor.py文件不存在")
                return
            
            with open(processor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查新增的方法
            required_methods = [
                "_get_job_detail",
                "_navigate_to_job_list", 
                "_filter_open_jobs",
                "_open_job_detail",
                "_parse_job_detail",
                "_close_job_detail_dialog"
            ]
            
            missing_methods = []
            for method in required_methods:
                if f"async def {method}" not in content:
                    missing_methods.append(method)
            
            if missing_methods:
                self.record_test_result(test_name, False, f"缺少方法: {', '.join(missing_methods)}")
            else:
                self.record_test_result(test_name, True, "所有必需的方法都已添加")
            
            # 检查调用逻辑
            if "job_detail = await self._get_job_detail(job_id)" in content:
                self.record_test_result("调用逻辑检查", True, "职位详情获取调用已正确添加")
            else:
                self.record_test_result("调用逻辑检查", False, "未找到职位详情获取调用")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def test_method_imports(self):
        """测试方法导入是否正确"""
        test_name = "方法导入测试"
        
        try:
            # 尝试导入JobFilterProcessor类
            from src.core.task_processor import JobFilterProcessor
            
            # 检查新增的方法是否存在
            processor = JobFilterProcessor(None, None, None)
            
            required_methods = [
                "_get_job_detail",
                "_navigate_to_job_list", 
                "_filter_open_jobs",
                "_open_job_detail",
                "_parse_job_detail",
                "_close_job_detail_dialog"
            ]
            
            missing_methods = []
            for method in required_methods:
                if not hasattr(processor, method):
                    missing_methods.append(method)
            
            if missing_methods:
                self.record_test_result(test_name, False, f"方法不存在: {', '.join(missing_methods)}")
            else:
                self.record_test_result(test_name, True, "所有方法都可以正确导入")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"导入异常: {str(e)}")
    
    def test_playwright_selectors(self):
        """测试Playwright选择器语法"""
        test_name = "Playwright选择器语法测试"
        
        try:
            processor_file = Path("src/core/task_processor.py")
            with open(processor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查Playwright API的使用
            playwright_apis = [
                "self.page.goto",
                "self.page.wait_for_selector",
                "self.page.locator",
                "self.page.frame_locator",
                "await.*\.click()",
                "await.*\.text_content()",
                "await.*\.is_visible()"
            ]
            
            missing_apis = []
            for api in playwright_apis:
                import re
                if not re.search(api, content):
                    missing_apis.append(api)
            
            if missing_apis:
                self.record_test_result(test_name, False, f"缺少Playwright API: {', '.join(missing_apis)}")
            else:
                self.record_test_result(test_name, True, "Playwright API使用正确")
            
            # 检查选择器格式
            selectors_to_check = [
                "iframe[src*='/web/frame/job/list-new']",
                "li[data-id='{job_id}']",
                ".job-operate-wrapper.opreat-btn",
                ".dialog-wrap.active .job-require-box"
            ]
            
            for selector in selectors_to_check:
                if selector.replace("{job_id}", "test") in content:
                    continue
                else:
                    self.record_test_result("选择器检查", False, f"选择器可能有问题: {selector}")
                    return
            
            self.record_test_result("选择器检查", True, "选择器格式正确")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def test_error_handling(self):
        """测试错误处理逻辑"""
        test_name = "错误处理逻辑测试"
        
        try:
            processor_file = Path("src/core/task_processor.py")
            with open(processor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查异常处理
            error_handling_patterns = [
                "try:",
                "except Exception as e:",
                "logger.error",
                "logger.warning",
                "return {}"
            ]
            
            missing_patterns = []
            for pattern in error_handling_patterns:
                if pattern not in content:
                    missing_patterns.append(pattern)
            
            if missing_patterns:
                self.record_test_result(test_name, False, f"缺少错误处理: {', '.join(missing_patterns)}")
            else:
                self.record_test_result(test_name, True, "错误处理逻辑完整")
            
            # 检查返回值处理
            if "if not job_detail:" in content and "logger.warning" in content:
                self.record_test_result("返回值处理", True, "返回值处理逻辑正确")
            else:
                self.record_test_result("返回值处理", False, "返回值处理逻辑可能有问题")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def test_data_parsing_logic(self):
        """测试数据解析逻辑"""
        test_name = "数据解析逻辑测试"
        
        try:
            processor_file = Path("src/core/task_processor.py")
            with open(processor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查数据字段解析
            expected_fields = [
                "jobId",
                "jobkind", 
                "jobName",
                "jobDescription",
                "jobType",
                "jobLocation",
                "jobExperience",
                "jobEducation",
                "jobMinMonthlySalary",
                "jobMaxMonthlySalary",
                "jobPayrollMonths",
                "jobKeywords"
            ]
            
            missing_fields = []
            for field in expected_fields:
                if f'"{field}"' not in content:
                    missing_fields.append(field)
            
            if missing_fields:
                self.record_test_result(test_name, False, f"缺少字段解析: {', '.join(missing_fields)}")
            else:
                self.record_test_result(test_name, True, "数据字段解析完整")
            
            # 检查字符串处理逻辑
            string_processing = [
                ".split(",
                ".strip()",
                "text_content()"
            ]
            
            for process in string_processing:
                if process not in content:
                    self.record_test_result("字符串处理", False, f"缺少字符串处理: {process}")
                    return
            
            self.record_test_result("字符串处理", True, "字符串处理逻辑正确")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def test_integration_with_existing_code(self):
        """测试与现有代码的集成"""
        test_name = "代码集成测试"
        
        try:
            processor_file = Path("src/core/task_processor.py")
            with open(processor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否正确集成到execute_task方法中
            integration_checks = [
                "job_detail = await self._get_job_detail(job_id)",
                "if not job_detail:",
                '"job_detail": job_detail'
            ]
            
            missing_integration = []
            for check in integration_checks:
                if check not in content:
                    missing_integration.append(check)
            
            if missing_integration:
                self.record_test_result(test_name, False, f"集成不完整: {', '.join(missing_integration)}")
            else:
                self.record_test_result(test_name, True, "与现有代码集成正确")
            
            # 检查是否保持了原有的执行流程
            if "await fetch_recommended_geeks(" in content:
                self.record_test_result("执行流程保持", True, "原有执行流程保持不变")
            else:
                self.record_test_result("执行流程保持", False, "原有执行流程可能被破坏")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("职位详情获取功能测试")
        print("=" * 50)
        
        # 运行各项测试
        self.test_code_structure()
        self.test_method_imports()
        self.test_playwright_selectors()
        self.test_error_handling()
        self.test_data_parsing_logic()
        self.test_integration_with_existing_code()
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"\n测试完成统计:")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        # 显示失败的测试
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        return failed_tests == 0
    
    def print_detailed_results(self):
        """打印详细测试结果"""
        print("\n" + "="*60)
        print("职位详情获取功能详细测试结果")
        print("="*60)
        
        for result in self.test_results:
            status = "PASS" if result["success"] else "FAIL"
            print(f"[{status}] {result['test_name']}")
            if result["message"]:
                print(f"      {result['message']}")
        
        print("="*60)


def main():
    """主函数"""
    print("SRA项目职位详情获取功能测试")
    print("验证task_processor.py中新增的职位详情获取逻辑")
    print("=" * 60)
    
    tester = JobDetailFeatureTester()
    
    try:
        success = tester.run_all_tests()
        
        # 打印详细结果
        tester.print_detailed_results()
        
        if success:
            print("\n🎉 所有测试通过！职位详情获取功能正常。")
            print("✅ 代码结构正确")
            print("✅ 方法导入正常")
            print("✅ Playwright API使用正确")
            print("✅ 错误处理完善")
            print("✅ 数据解析逻辑完整")
            print("✅ 与现有代码集成良好")
            print("\n📝 功能说明:")
            print("- 在jobFilterTrigger任务执行前获取职位详情")
            print("- 支持进入职位列表页面")
            print("- 支持筛选开放中职位")
            print("- 支持点击指定jobId进入详情页面")
            print("- 支持解析完整的职位详情数据")
            print("- 支持优雅的错误处理和资源清理")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查职位详情获取功能实现。")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
快速验证简历详情API接口的脚本
"""
import requests
import json
import time


def quick_test():
    """快速测试API接口"""
    print("Boss直聘简历详情API快速测试")
    print("=" * 40)
    
    # API配置
    api_url = "http://localhost:8000/agent/resume/detail"
    test_url = "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
    
    # 检查API服务是否运行
    try:
        health_check = requests.get("http://localhost:8000/docs", timeout=5)
        if health_check.status_code == 200:
            print("✅ API服务正在运行")
        else:
            print("⚠️  API服务状态异常")
    except:
        print("❌ 无法连接到API服务")
        print("请确保API服务正在运行: uvicorn main:app --reload")
        return
    
    # 测试API接口
    print(f"\n测试URL: {test_url}")
    print("发送请求...")
    
    start_time = time.time()
    
    try:
        response = requests.post(
            api_url,
            json={"resume_detail_url": test_url},
            headers={"Content-Type": "application/json"},
            timeout=70
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"请求耗时: {duration:.2f}秒")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"API响应码: {result.get('code')}")
            print(f"API消息: {result.get('message')}")
            
            api_result = result.get('result', {})
            
            if api_result.get('status') == 'success':
                print("\n✅ 测试成功!")
                
                resume_data = api_result.get('data', {})
                print(f"任务ID: {api_result.get('task_id')}")
                print(f"姓名: {resume_data.get('geekName', '未知')}")
                print(f"年龄: {resume_data.get('ageDesc', '未知')}")
                print(f"工作年限: {resume_data.get('workYearDesc', '未知')}")
                print(f"期望薪资: {resume_data.get('salaryDesc', '未知')}")
                print(f"期望职位: {resume_data.get('workEduDesc', '未知')}")
                
                # 显示部分简历描述
                geek_desc = resume_data.get('geekDesc', '')
                if geek_desc:
                    print(f"简历描述: {geek_desc[:100]}...")
                
                print("\n🎉 API接口工作正常!")
                
            else:
                print(f"\n❌ 测试失败!")
                print(f"错误信息: {api_result.get('message')}")
                
                # 分析错误类型
                error_msg = api_result.get('message', '').lower()
                if '正在运行' in error_msg:
                    print("💡 提示: 检测到并发冲突，请稍后重试")
                elif '缺少' in error_msg:
                    print("💡 提示: 参数错误")
                elif '获取' in error_msg:
                    print("💡 提示: 数据获取失败，可能是网络问题")
        else:
            print(f"\n❌ HTTP请求失败!")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print(f"\n❌ 请求超时!")
        print("💡 提示: 可能是网络较慢或服务处理时间较长")
        
    except requests.exceptions.ConnectionError:
        print(f"\n❌ 连接失败!")
        print("💡 提示: 请确保API服务正在运行")
        
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")


def test_error_cases():
    """测试错误情况"""
    print("\n" + "=" * 40)
    print("测试错误处理")
    print("=" * 40)
    
    api_url = "http://localhost:8000/agent/resume/detail"
    
    # 测试空URL
    print("\n1. 测试空URL...")
    try:
        response = requests.post(
            api_url,
            json={"resume_detail_url": ""},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 400:
                print("✅ 正确处理了空URL")
            else:
                print("⚠️  空URL处理可能有问题")
        else:
            print(f"❌ HTTP状态码异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试空URL失败: {e}")
    
    # 测试无效URL
    print("\n2. 测试无效URL...")
    try:
        response = requests.post(
            api_url,
            json={"resume_detail_url": "invalid-url"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            api_result = result.get('result', {})
            if api_result.get('status') == 'error':
                print("✅ 正确处理了无效URL")
            else:
                print("⚠️  无效URL处理可能有问题")
        else:
            print(f"❌ HTTP状态码异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试无效URL失败: {e}")


def main():
    """主函数"""
    print("选择测试模式:")
    print("1. 快速功能测试")
    print("2. 错误处理测试")
    print("3. 完整测试")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        quick_test()
    elif choice == "2":
        test_error_cases()
    elif choice == "3":
        quick_test()
        test_error_cases()
    else:
        print("默认运行快速测试...")
        quick_test()


if __name__ == "__main__":
    main()

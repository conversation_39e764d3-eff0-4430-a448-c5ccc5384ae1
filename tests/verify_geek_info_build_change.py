#!/usr/bin/env python3
"""
验证geek_info_build.py中isPartTime修改的脚本
"""
import re
from pathlib import Path

def verify_isparttime_change():
    """验证isPartTime的修改"""
    print("验证geek_info_build.py中isPartTime的修改")
    print("=" * 50)
    
    file_path = Path("src/flows/geek_info_build.py")
    
    if not file_path.exists():
        print("❌ geek_info_build.py文件不存在")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修改是否正确
        checks = [
            {
                "name": "expectations中的isPartTime赋值",
                "pattern": r"expectation\['isPartTime'\]\s*=\s*text_exps_details\.get\('isPartTime',\s*\[\]\)",
                "expected": True,
                "description": "检查是否使用了text_exps_details.get('isPartTime', [])"
            },
            {
                "name": "text_exps_details变量定义",
                "pattern": r"text_exps_details\s*=\s*self\._parse_details_from_text\(\)",
                "expected": True,
                "description": "检查text_exps_details是否正确定义"
            },
            {
                "name": "_parse_details_from_text中的isPartTime",
                "pattern": r"result\['isPartTime'\]\s*=\s*isPartTime",
                "expected": True,
                "description": "检查_parse_details_from_text方法中是否设置了isPartTime"
            }
        ]
        
        all_passed = True
        
        for check in checks:
            found = bool(re.search(check["pattern"], content))
            expected = check["expected"]
            
            if found == expected:
                status = "✅ PASS"
            else:
                status = "❌ FAIL"
                all_passed = False
            
            print(f"{status} - {check['name']}: {check['description']}")
            if found:
                # 显示找到的匹配内容
                match = re.search(check["pattern"], content)
                if match:
                    print(f"      找到: {match.group(0)}")
        
        # 显示相关代码段
        print("\n代码段预览:")
        print("-" * 40)
        
        # 查找expectations处理的代码段
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'text_expectations' in line and '_parse_expectations_from_text' in line:
                # 显示前后几行
                start = max(0, i - 2)
                end = min(len(lines), i + 8)
                for j in range(start, end):
                    prefix = ">>> " if j == i or 'isPartTime' in lines[j] else "    "
                    print(f"{prefix}{j+1:3d}: {lines[j]}")
                break
        
        print("-" * 40)
        
        if all_passed:
            print("\n🎉 所有检查通过！")
            print("✅ isPartTime已正确修改为使用text_exps_details.get('isPartTime', [])")
            print("✅ 修改符合要求")
        else:
            print("\n❌ 部分检查失败，请检查修改")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def show_change_summary():
    """显示修改总结"""
    print("\n修改总结:")
    print("-" * 30)
    print("修改位置: src/flows/geek_info_build.py")
    print("修改内容: final_result['candidate']['expectations']中的isPartTime字段")
    print("修改前: 没有isPartTime字段")
    print("修改后: expectation['isPartTime'] = text_exps_details.get('isPartTime', [])")
    print("\n说明:")
    print("- text_exps_details来自_parse_details_from_text()方法")
    print("- _parse_details_from_text()方法会解析教育经历中的'非全日制'信息")
    print("- 如果找到'非全日制'，isPartTime为'1'，否则为''")
    print("- 使用.get('isPartTime', [])确保即使没有该字段也返回空数组")

def main():
    """主函数"""
    success = verify_isparttime_change()
    show_change_summary()
    
    if success:
        print("\n✅ 验证成功！isPartTime修改已正确完成。")
        return 0
    else:
        print("\n❌ 验证失败！请检查修改是否正确。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)

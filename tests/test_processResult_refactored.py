#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改造后的processResult函数
"""

import sys
import os
import unittest
from unittest.mock import patch

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.flows.geek_filter import processResult, getFullUrlRandom


class TestProcessResultRefactored(unittest.TestCase):
    """测试改造后的processResult函数"""
    
    def setUp(self):
        """测试前准备"""
        pass
        
    def test_processResult_hi_continue(self):
        """测试打招呼且继续的情况"""
        print("\n=== 测试打招呼且继续 ===")
        
        result = processResult(isContinue="1", status="1", process=5, matchprocess=3)
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertIn("hi", resultUrl)  # 包含hi URL
        self.assertFalse(success)  # 未完成
        
    def test_processResult_hi_stop(self):
        """测试打招呼且停止的情况"""
        print("\n=== 测试打招呼且停止 ===")
        
        result = processResult(isContinue="0", status="1", process=5, matchprocess=3)
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "2")  # 结束
        self.assertIn("hi_over_stop", resultUrl)  # 包含hi_over_stop URL
        self.assertTrue(success)  # 完成
        
    def test_processResult_no_hi_continue(self):
        """测试不打招呼且继续的情况"""
        print("\n=== 测试不打招呼且继续 ===")
        
        result = processResult(isContinue="1", status="0", process=5, matchprocess=3)
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertIn("next", resultUrl)  # 包含next URL
        self.assertFalse(success)  # 未完成
        
    def test_processResult_no_hi_stop(self):
        """测试不打招呼且停止的情况"""
        print("\n=== 测试不打招呼且停止 ===")
        
        result = processResult(isContinue="0", status="0", process=5, matchprocess=3)
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续（默认）
        self.assertEqual(resultUrl, "")  # 无URL
        self.assertTrue(success)  # 完成
        
    def test_processResult_fav_continue(self):
        """测试收藏且继续的情况"""
        print("\n=== 测试收藏且继续 ===")
        
        result = processResult(isContinue="1", status="2", process=5, matchprocess=3)
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertIn("fav_save", resultUrl)  # 包含fav_save URL
        self.assertFalse(success)  # 未完成
        
    def test_processResult_fav_stop(self):
        """测试收藏且停止的情况"""
        print("\n=== 测试收藏且停止 ===")
        
        result = processResult(isContinue="0", status="2", process=5, matchprocess=3)
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "2")  # 结束
        self.assertIn("fav_save_stop", resultUrl)  # 包含fav_save_stop URL
        self.assertTrue(success)  # 完成
        
    def test_processResult_batch_hi_continue(self):
        """测试批量打招呼且继续的情况"""
        print("\n=== 测试批量打招呼且继续 ===")
        
        batch_ids = ["candidate1", "candidate2", "candidate3"]
        result = processResult(isContinue="1", status="3", process=5, matchprocess=3, 
                             batchHiCandicateIds=batch_ids)
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertIn("batchHi", resultUrl)  # 包含batchHi URL
        self.assertFalse(success)  # 未完成
        
    def test_processResult_batch_hi_stop(self):
        """测试批量打招呼且停止的情况"""
        print("\n=== 测试批量打招呼且停止 ===")
        
        batch_ids = ["candidate1", "candidate2", "candidate3"]
        result = processResult(isContinue="0", status="3", process=5, matchprocess=3, 
                             batchHiCandicateIds=batch_ids)
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "2")  # 结束
        self.assertIn("batchHi_over_stop", resultUrl)  # 包含batchHi_over_stop URL
        self.assertTrue(success)  # 完成
        
    def test_processResult_batch_hi_empty(self):
        """测试批量打招呼但ID列表为空的情况"""
        print("\n=== 测试批量打招呼但ID列表为空 ===")
        
        result = processResult(isContinue="1", status="3", process=5, matchprocess=3, 
                             batchHiCandicateIds=[])
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续
        self.assertIn("batchHi", resultUrl)  # 包含batchHi URL
        self.assertFalse(success)  # 未完成
        
    def test_processResult_default_params(self):
        """测试默认参数的情况"""
        print("\n=== 测试默认参数 ===")
        
        result = processResult()
        runStatus, resultUrl, success = result
        
        print(f"结果: runStatus={runStatus}, resultUrl={resultUrl}, success={success}")
        
        self.assertEqual(runStatus, "1")  # 继续（默认）
        self.assertEqual(resultUrl, "")  # 无URL
        self.assertTrue(success)  # 完成
        
    def test_getFullUrlRandom(self):
        """测试getFullUrlRandom函数"""
        print("\n=== 测试getFullUrlRandom函数 ===")
        
        base_url = "https://www.zhipin.com/web/chat/hi"
        full_url = getFullUrlRandom(base_url)
        
        print(f"基础URL: {base_url}")
        print(f"完整URL: {full_url}")
        
        # 验证URL格式
        self.assertIn(base_url, full_url)
        self.assertIn("_t=", full_url)
        self.assertIn("r=", full_url)
        
        # 测试带参数的URL
        base_url_with_params = "https://www.zhipin.com/web/chat/hi?param=value"
        full_url_with_params = getFullUrlRandom(base_url_with_params)
        
        print(f"带参数基础URL: {base_url_with_params}")
        print(f"带参数完整URL: {full_url_with_params}")
        
        self.assertIn(base_url_with_params, full_url_with_params)
        self.assertIn("&_t=", full_url_with_params)
        self.assertIn("&r=", full_url_with_params)


def run_all_tests():
    """运行所有测试"""
    print("开始测试改造后的processResult函数...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试用例
    test_cases = [
        TestProcessResultRefactored("test_processResult_hi_continue"),
        TestProcessResultRefactored("test_processResult_hi_stop"),
        TestProcessResultRefactored("test_processResult_no_hi_continue"),
        TestProcessResultRefactored("test_processResult_no_hi_stop"),
        TestProcessResultRefactored("test_processResult_fav_continue"),
        TestProcessResultRefactored("test_processResult_fav_stop"),
        TestProcessResultRefactored("test_processResult_batch_hi_continue"),
        TestProcessResultRefactored("test_processResult_batch_hi_stop"),
        TestProcessResultRefactored("test_processResult_batch_hi_empty"),
        TestProcessResultRefactored("test_processResult_default_params"),
        TestProcessResultRefactored("test_getFullUrlRandom"),
    ]
    
    for test_case in test_cases:
        test_suite.addTest(test_case)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n测试结果: 运行 {result.testsRun} 个测试")
    print(f"失败: {len(result.failures)} 个")
    print(f"错误: {len(result.errors)} 个")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_all_tests()
    if success:
        print("\n✓ 所有测试通过！")
    else:
        print("\n✗ 部分测试失败！") 
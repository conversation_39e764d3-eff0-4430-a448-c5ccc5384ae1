#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改造后的geek_filter函数
"""

import asyncio
import json
import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.flows.geek_filter import geek_filter, recodeTriggerResult, processResult


class TestGeekFilterRefactored(unittest.TestCase):
    """测试改造后的geek_filter函数"""
    
    def setUp(self):
        """测试前准备"""
        # 设置环境变量
        os.environ['account_name'] = 'test_account'
        os.environ['bn_login_name'] = 'test_bn_login'
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    @patch('src.flows.geek_filter.requests.post')
    @patch('src.flows.callback.error_callback')
    def test_geek_filter_success(self, mock_error_callback, mock_post):
        """测试成功情况"""
        # 模拟成功的响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "message": "success",
            "result": {
                "batchNo": "test_batch_001",
                "status": "1",
                "isContinue": "1",
                "isConflict": "0",
                "batchStatus": "processing",
                "batchReviewNum": 10,
                "batchMatchedNum": 5,
                "batchHiCandicateIds": ["candidate1", "candidate2"],
                "batchHiIds": ["hi1", "hi2"],
                "isSkip": "0"
            }
        }
        mock_response.content.decode.return_value = json.dumps(mock_response.json.return_value)
        mock_post.return_value = mock_response
        
        # 执行测试
        async def test_func():
            result = await geek_filter(
                batchNo="test_batch_001",
                geek_info={"test": "data"},
                job_detail={"job": "detail"},
                jobId="test_job_001",
                storePath=self.temp_dir,
                process=5,
                matchprocess=3,
                isLast=False
            )
            return result
            
        result = asyncio.run(test_func())
        
        # 验证结果
        self.assertIsNotNone(result)
        status, isContinue, batchHiCandicateIds, batchHiIds = result
        self.assertEqual(status, "1")
        self.assertEqual(isContinue, "1")
        self.assertEqual(batchHiCandicateIds, ["candidate1", "candidate2"])
        self.assertEqual(batchHiIds, ["hi1", "hi2"])
        
        # 验证文件是否被创建
        expected_file = os.path.join(self.temp_dir, "jobNums_test_batch_001")
        self.assertTrue(os.path.exists(expected_file))
        
    @patch('src.flows.geek_filter.requests.post')
    @patch('src.flows.callback.error_callback')
    def test_geek_filter_http_error(self, mock_error_callback, mock_post):
        """测试HTTP错误情况"""
        # 模拟HTTP错误
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_post.return_value = mock_response
        
        # 执行测试
        async def test_func():
            result = await geek_filter(
                batchNo="test_batch_001",
                geek_info={"test": "data"},
                job_detail={"job": "detail"},
                jobId="test_job_001",
                storePath=self.temp_dir
            )
            return result
            
        result = asyncio.run(test_func())
        
        # 验证结果
        self.assertIsNone(result)
        mock_error_callback.assert_called_once_with("test_batch_001", "访问筛选接口异常中断", "10")
        
        # 验证触发结果文件是否被创建
        expected_file = os.path.join(self.temp_dir, "trigger_result_test_batch_001.json")
        self.assertTrue(os.path.exists(expected_file))
        
    @patch('src.flows.geek_filter.requests.post')
    def test_geek_filter_service_unavailable(self, mock_post):
        """测试服务不可用情况"""
        # 模拟服务不可用响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 1,
            "message": "服务暂不可用"
        }
        mock_response.content.decode.return_value = json.dumps(mock_response.json.return_value)
        mock_post.return_value = mock_response
        
        # 执行测试
        async def test_func():
            with self.assertRaises(ValueError) as context:
                await geek_filter(
                    batchNo="test_batch_001",
                    geek_info={"test": "data"},
                    job_detail={"job": "detail"}
                )
            return str(context.exception)
            
        error_msg = asyncio.run(test_func())
        self.assertEqual(error_msg, "SRAA服务暂时不可用")
        
    def test_recodeTriggerResult(self):
        """测试记录触发结果函数"""
        batchNo = "test_batch_001"
        actionStatus = "3"
        jobId = "test_job_001"
        storePath = self.temp_dir
        bnLoginName = "test_bn_login"
        
        # 执行测试
        recodeTriggerResult(batchNo, actionStatus, jobId, storePath, bnLoginName)
        
        # 验证文件是否被创建
        expected_file = os.path.join(self.temp_dir, "trigger_result_test_batch_001.json")
        self.assertTrue(os.path.exists(expected_file))
        
        # 验证文件内容
        with open(expected_file, 'r') as f:
            data = json.load(f)
            self.assertEqual(data["batchNo"], batchNo)
            self.assertEqual(data["actionStatus"], actionStatus)
            self.assertEqual(data["jobId"], jobId)
            self.assertEqual(data["bnLoginName"], bnLoginName)
            
    def test_processResult(self):
        """测试处理结果函数"""
        # 测试成功情况
        result = processResult("1", "1")
        self.assertIsInstance(result, tuple)
        self.assertEqual(len(result), 3)
        
        # 测试失败情况
        with patch('src.flows.geek_filter.logger.error') as mock_logger:
            result = processResult("0", "0")
            self.assertIsInstance(result, tuple)
            self.assertEqual(len(result), 3)


if __name__ == '__main__':
    unittest.main() 
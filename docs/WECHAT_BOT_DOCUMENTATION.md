# SRA企业微信机器人监控系统

## 概述

SRA项目已将原有的邮件通知系统升级为企业微信机器人监控系统，提供实时的监控告警、任务通知和错误报告功能。

## 功能特性

### 🤖 **企业微信机器人集成**
- **实时消息推送**: 支持文本、Markdown格式消息
- **截图功能**: 自动捕获页面截图并发送
- **多种消息类型**: 监控告警、任务通知、系统状态等
- **智能重试**: 网络异常时自动重试发送

### 📊 **监控告警功能**
- **分级告警**: info、warning、error、critical四个级别
- **详细信息**: 包含错误详情、堆栈跟踪、系统信息
- **可视化展示**: 使用emoji和Markdown格式美化消息
- **截图附件**: 自动附带当前浏览器截图

### 📋 **任务通知功能**
- **任务状态**: 成功、失败、超时等状态通知
- **执行详情**: 任务ID、类型、耗时、账户信息
- **实时更新**: 任务开始、进行中、完成全程跟踪

## 配置信息

### Webhook地址
```
https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=94bf57fa-68af-42d0-8736-6070c5f6b2d1
```

### 消息限制
- **文本消息**: 最大4096字符
- **图片大小**: 最大2MB
- **发送频率**: 建议间隔1秒以上

## 使用方法

### 1. 基本消息发送

```python
from src.utils.mailer import get_wechat_bot

bot = get_wechat_bot()

# 发送文本消息
await bot.send_text_message("这是一条测试消息")

# 发送Markdown消息
markdown_content = """## 标题
**粗体文本**
- 列表项1
- 列表项2
"""
await bot.send_markdown_message(markdown_content)
```

### 2. 监控告警

```python
from src.utils.mailer import send_monitoring_alert

# 发送监控告警（包含截图）
await send_monitoring_alert(
    title="系统异常告警",
    error_details="详细的错误信息...",
    page=page_object,  # Playwright页面对象
    severity="error"   # info/warning/error/critical
)
```

### 3. 任务通知

```python
from src.utils.mailer import send_task_notification

# 发送任务通知
await send_task_notification(
    task_id="task_001",
    task_type="get_resume_detail",
    status="success",
    message="任务执行成功",
    duration=3.45,
    account_name="test_account"
)
```

### 4. 截图发送

```python
# 发送页面截图
screenshot_data = await page.screenshot()
await bot.send_image_message(screenshot_data)
```

## 消息格式示例

### 监控告警消息
```markdown
## ⚠️ SRA监控告警

**告警时间**: 2024-01-15 10:30:00
**严重程度**: WARNING
**告警标题**: 登录失败

### 错误详情
```
账户登录超时
网络连接异常
重试3次后仍然失败
```

### 附加信息
- **服务器**: sra-worker-01
- **进程ID**: 12345
- **内存使用**: 256MB
```

### 任务通知消息
```markdown
## ✅ SRA任务通知

**时间**: 2024-01-15 10:30:00
**任务ID**: resume_001
**任务类型**: get_resume_detail
**状态**: SUCCESS
**账户**: test_account
**耗时**: 2.35秒

**详情**: 简历详情获取成功
```

## 集成点

### 1. 任务处理器集成
- **任务成功**: 自动发送成功通知
- **任务失败**: 自动发送失败告警
- **任务异常**: 自动发送异常告警（包含截图）

### 2. Worker异常集成
- **严重错误**: 发送critical级别告警
- **网络异常**: 发送warning级别告警
- **系统异常**: 发送error级别告警

### 3. 浏览器异常集成
- **页面加载失败**: 自动截图并发送告警
- **元素定位失败**: 发送详细错误信息
- **脚本执行异常**: 包含堆栈跟踪信息

## 测试验证

### 快速验证
```bash
# 运行快速验证脚本
python quick_test_wechat.py
```

### 完整测试
```bash
# 运行完整功能测试
python test_wechat_bot.py
```

### 测试项目
- ✅ 连接性测试
- ✅ 文本消息发送
- ✅ Markdown消息发送
- ✅ 截图功能测试
- ✅ 监控告警测试
- ✅ 任务通知测试

## 故障排除

### 常见问题

#### 1. 消息发送失败
**症状**: 返回errcode非0
**解决**: 
- 检查webhook URL是否正确
- 确认企业微信群机器人是否正常
- 检查网络连接

#### 2. 截图发送失败
**症状**: 图片消息发送失败
**解决**:
- 检查图片大小是否超过2MB
- 确认页面是否正常加载
- 检查浏览器状态

#### 3. 消息格式异常
**症状**: Markdown格式显示不正确
**解决**:
- 检查Markdown语法
- 确认特殊字符是否正确转义
- 验证消息长度是否超限

### 调试方法

#### 1. 启用详细日志
```python
import logging
logging.getLogger('src.utils.mailer').setLevel(logging.DEBUG)
```

#### 2. 测试连接性
```python
# 测试基本连接
await bot.send_text_message("连接测试")
```

#### 3. 检查响应
```python
# 查看详细响应信息
response_data = await bot._send_request(payload)
print(response_data)
```

## 性能优化

### 1. 消息发送优化
- **批量发送**: 避免短时间内大量发送
- **异步处理**: 使用asyncio.create_task异步发送
- **重试机制**: 指数退避重试策略

### 2. 截图优化
- **大小控制**: 限制截图文件大小
- **质量调整**: 根据需要调整截图质量
- **区域截图**: 只截取必要区域

### 3. 内存管理
- **及时清理**: 发送后及时清理截图数据
- **连接复用**: 复用HTTP连接
- **超时控制**: 设置合理的超时时间

## 安全考虑

### 1. 敏感信息保护
- **数据脱敏**: 自动过滤敏感信息
- **截图安全**: 避免截图包含敏感数据
- **日志安全**: 不在日志中记录敏感信息

### 2. 访问控制
- **Webhook安全**: 使用企业微信提供的安全机制
- **消息验证**: 验证消息来源和完整性
- **权限控制**: 限制机器人权限范围

## 迁移说明

### 从邮件系统迁移
1. **保持兼容**: 原有的send_email函数仍然可用
2. **逐步迁移**: 建议逐步替换为新的企业微信接口
3. **功能增强**: 新系统提供更丰富的功能

### 迁移步骤
1. 安装依赖: `pip install aiofiles`
2. 运行测试: `python quick_test_wechat.py`
3. 验证功能: 确认消息正常发送
4. 更新代码: 替换邮件调用为企业微信调用

## 总结

企业微信机器人监控系统为SRA项目提供了：

- **实时性**: 即时推送监控信息
- **可视化**: 丰富的消息格式和截图功能
- **可靠性**: 完善的重试和错误处理机制
- **易用性**: 简单的API接口和兼容性设计

通过企业微信机器人，运维团队可以及时了解系统状态，快速响应异常情况，提升系统的可观测性和可维护性。

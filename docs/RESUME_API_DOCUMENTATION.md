# Boss直聘简历详情API接口文档

## 接口概述

新增的简历详情获取API接口，用于通过Boss直聘简历分享URL获取详细的简历信息。

## 接口信息

- **接口路径**: `/agent/resume/detail`
- **请求方法**: `POST`
- **内容类型**: `application/json`
- **超时时间**: 60秒

## 请求参数

### 请求体 (JSON)

```json
{
    "resume_detail_url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| resume_detail_url | string | 是 | Boss直聘简历分享页面的完整URL |

## 响应格式

### 成功响应

```json
{
    "timestamp": "1705123456789",
    "code": 0,
    "message": "简历详情获取成功",
    "result": {
        "status": "success",
        "task_id": "uuid-task-id",
        "data": {
            "shareId": 35910040,
            "geekName": "闫龙",
            "ageDesc": "30岁",
            "gender": 1,
            "workYearDesc": "8年",
            "geekDesc": "1，热爱编程工作，自学能力强，能够快速学习新技术并且应用于实际工作中。\n2，需求理解力强，善于在项目中总结方法，提升代码执行效率。\n3，在完成新需求时多次进行本地测试，保证代码的强壮完整，减少bug产生原因。\n4，具有团队交流和合作意识，妥善的处理人际关系，工作抗压接受加班。",
            "remarkCount": 0,
            "salaryDesc": "16-20K",
            "gray": 0,
            "workEduDesc": "期望职位 Java",
            "applyStatus": 2,
            "activeTimeDesc": "刚刚活跃",
            "englishDesc": null,
            "showSort": [1, 2, 3],
            "attachmentResume": 1,
            "suffixName": "docx",
            "designGreeting": 0,
            "designSuffixName": null,
            "videoResume": 0
        }
    }
}
```

### 错误响应

#### 参数错误 (400)
```json
{
    "timestamp": "1705123456789",
    "code": 400,
    "message": "缺少必要的resume_detail_url参数",
    "result": {
        "status": "error",
        "message": "缺少必要的resume_detail_url参数"
    }
}
```

#### 并发冲突 (409)
```json
{
    "timestamp": "1705123456789",
    "code": 409,
    "message": "有任务正在运行，请稍后再试",
    "result": {
        "status": "error",
        "message": "另一个任务正在运行，请稍后再试"
    }
}
```

#### 服务不可用 (503)
```json
{
    "timestamp": "1705123456789",
    "code": 503,
    "message": "Worker服务不可用",
    "result": {
        "status": "error",
        "message": "Worker服务不可用"
    }
}
```

#### 处理失败 (500)
```json
{
    "timestamp": "1705123456789",
    "code": 500,
    "message": "获取简历详情失败",
    "result": {
        "status": "error",
        "message": "未能获取到简历数据",
        "task_id": "uuid-task-id"
    }
}
```

## 响应字段说明

### 基础响应字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| timestamp | string | 响应时间戳（毫秒） |
| code | integer | 响应状态码，0表示成功 |
| message | string | 响应消息 |
| result | object | 具体结果数据 |

### result字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| status | string | 处理状态：success/error |
| task_id | string | 任务唯一标识符 |
| data | object | 简历详情数据（仅成功时存在） |
| message | string | 错误信息（仅失败时存在） |

### 简历数据字段 (data)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| shareId | integer | 分享ID |
| geekName | string | 求职者姓名 |
| ageDesc | string | 年龄描述 |
| gender | integer | 性别（1=男，0=女） |
| workYearDesc | string | 工作年限描述 |
| geekDesc | string | 个人描述/简历内容 |
| remarkCount | integer | 备注数量 |
| salaryDesc | string | 期望薪资 |
| workEduDesc | string | 期望职位描述 |
| applyStatus | integer | 申请状态 |
| activeTimeDesc | string | 活跃时间描述 |
| attachmentResume | integer | 是否有附件简历（1=有，0=无） |
| suffixName | string | 附件简历文件后缀 |
| videoResume | integer | 是否有视频简历（1=有，0=无） |

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8000/agent/resume/detail" \
     -H "Content-Type: application/json" \
     -d '{
       "resume_detail_url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
     }'
```

### Python 示例

```python
import requests
import json

# 基本使用
url = "http://localhost:8000/agent/resume/detail"
data = {
    "resume_detail_url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
}

response = requests.post(url, json=data)
result = response.json()

if result['code'] == 0 and result['result']['status'] == 'success':
    resume_data = result['result']['data']
    print(f"姓名: {resume_data['geekName']}")
    print(f"期望薪资: {resume_data['salaryDesc']}")
else:
    print(f"获取失败: {result['message']}")
```

### JavaScript 示例

```javascript
const fetchResumeDetail = async (resumeUrl) => {
    try {
        const response = await fetch('http://localhost:8000/agent/resume/detail', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                resume_detail_url: resumeUrl
            })
        });
        
        const result = await response.json();
        
        if (result.code === 0 && result.result.status === 'success') {
            const resumeData = result.result.data;
            console.log(`姓名: ${resumeData.geekName}`);
            console.log(`期望薪资: ${resumeData.salaryDesc}`);
            return resumeData;
        } else {
            console.error(`获取失败: ${result.message}`);
            return null;
        }
    } catch (error) {
        console.error('请求异常:', error);
        return null;
    }
};

// 使用示例
fetchResumeDetail('https://www.zhipin.com/web/boss/resume/share?shareId=35910040');
```

## 错误处理建议

### 1. 并发冲突处理

当收到409错误时，建议实现重试机制：

```python
import time

def get_resume_with_retry(url, max_retries=3, delay=5):
    for attempt in range(max_retries):
        response = requests.post(api_url, json={"resume_detail_url": url})
        result = response.json()
        
        if result['code'] != 409:  # 非并发冲突
            return result
        
        if attempt < max_retries - 1:  # 不是最后一次尝试
            print(f"检测到并发冲突，{delay}秒后重试...")
            time.sleep(delay)
    
    return result
```

### 2. 超时处理

设置合适的请求超时时间：

```python
try:
    response = requests.post(url, json=data, timeout=70)
except requests.exceptions.Timeout:
    print("请求超时，请稍后重试")
```

### 3. 网络异常处理

```python
try:
    response = requests.post(url, json=data)
except requests.exceptions.ConnectionError:
    print("网络连接失败，请检查网络或服务状态")
except requests.exceptions.RequestException as e:
    print(f"请求异常: {e}")
```

## 性能和限制

### 1. 并发限制
- 同时只能处理一个简历详情获取任务
- 建议客户端实现队列机制串行处理

### 2. 超时设置
- API超时时间：60秒
- 建议客户端超时时间：70秒

### 3. 频率限制
- 建议请求间隔：3-5秒
- 避免过于频繁的请求

## 测试工具

项目提供了以下测试工具：

1. **`test_resume_api.py`** - 完整的API测试脚本
2. **`resume_api_client.py`** - 客户端封装和使用示例

### 运行测试

```bash
# 测试API功能
python test_resume_api.py

# 使用客户端示例
python resume_api_client.py

# 交互模式
python resume_api_client.py --interactive
```

## 注意事项

1. **URL格式**: 确保使用正确的Boss直聘简历分享URL格式
2. **网络环境**: 需要能够访问Boss直聘网站
3. **服务依赖**: 需要确保后台Worker服务正常运行
4. **数据时效性**: 简历数据可能会发生变化，建议适时更新
5. **隐私保护**: 请遵守相关法律法规，合理使用获取的简历数据

通过这个API接口，可以方便地集成Boss直聘简历详情获取功能到现有系统中。

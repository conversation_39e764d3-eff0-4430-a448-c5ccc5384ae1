# SRA任务管理功能说明

## 新增功能概述

本次更新添加了以下5个主要功能：

1. **处理单个简历耗时日志记录**
2. **配置文件统一管理所有延迟参数**
3. **浏览器操作tracing记录和回放**
4. **日志自动压缩归档**
5. **任务中断恢复管理**

## 1. 简历处理耗时日志

### 功能说明
- 自动记录每个简历处理的开始和结束时间
- 计算并记录处理耗时
- 在日志中显示详细的性能信息

### 日志示例
```
2024-01-15 10:30:15 INFO 开始处理详情页: 张三
2024-01-15 10:30:18 INFO 简历处理完成: 张三, 耗时: 3.25秒
```

## 2. 延迟参数配置化

### 配置文件位置
`src/conf/config.py` 中的 `DELAYS` 配置项

### 主要配置项
```python
DELAYS = {
    # 页面导航相关
    'page_navigation_delay': 1.0,
    'page_load_wait': 2.0,
    
    # 弹窗处理相关
    'dialog_reaction_time': 0.8,
    'dialog_reading_time': 1.5,
    'dialog_close_wait': 0.8,
    
    # 点击操作相关
    'click_before_delay': 0.5,
    'click_hover_delay': 0.3,
    'click_after_delay': 0.4,
    
    # 其他延迟配置...
}
```

### 使用方法
所有 `asyncio.sleep()` 调用都已改为使用配置文件中的参数：
```python
await asyncio.sleep(CONFIG.Crawler.DELAYS['page_load_wait'])
```

## 3. 浏览器操作Tracing记录

### 功能说明
- 自动记录所有浏览器操作
- 支持操作回放和调试
- 错误时自动保存trace文件

### 文件存储位置
- 正常trace: `./data/traces/trace_操作名称_时间戳.zip`
- 错误trace: `./data/traces/trace_error_错误类型_时间戳.zip`

### 使用方法
```python
from src.utils.tracing_manager import start_trace, stop_trace

# 开始记录特定操作
await start_trace(page, "login", "账号名")

# 执行操作...

# 停止记录
await stop_trace(page)
```

### Tracing架构说明
- **浏览器初始化时**：只初始化tracing管理器，不启动recording
- **具体操作时**：使用`start_trace()`启动针对特定操作的recording
- **操作完成时**：使用`stop_trace()`停止recording并保存文件
- **错误处理时**：自动保存错误trace文件

### 避免重复启动
系统已经修正了tracing重复启动的问题：
- `browser_manager.py`：只初始化tracing管理器
- `celery_worker.py`：移除了重复的tracing启动
- `tracing_manager.py`：智能处理已启动的tracing状态

### 回放trace文件
使用Playwright的trace viewer：
```bash
npx playwright show-trace ./data/traces/trace_login_20240115_103015.zip
```

## 4. 日志自动压缩归档

### 功能说明
- 每天凌晨2点自动压缩前一天的日志
- 每周日凌晨3点清理30天前的归档文件
- 支持手动归档操作

### 文件结构
```
logs/
├── sra_2024-01-15.log          # 当前日志
├── error_2024-01-15.log        # 错误日志
└── archive/                    # 归档目录
    ├── sra_2024-01-14.log.gz   # 压缩的历史日志
    └── error_2024-01-14.log.gz
```

### 手动操作
```bash
# 立即归档当前日志
python task_manager.py archive-logs

# 查看日志信息
python task_manager.py log-info
```

## 5. 任务中断恢复管理

### 功能说明
- 自动保存任务状态和进度
- 程序中断后可以恢复执行
- 支持任务暂停、恢复、删除等操作

### 任务状态文件
存储位置：`./data/task_states/任务ID.json`

### 管理命令

#### 查看所有任务
```bash
python task_manager.py list
```

#### 查看特定状态的任务
```bash
python task_manager.py list --status failed
python task_manager.py list --status paused
```

#### 查看任务详情
```bash
python task_manager.py detail fetch_geeks_12345_1705123456
```

#### 恢复中断的任务
```bash
python task_manager.py resume fetch_geeks_12345_1705123456
```

#### 暂停运行中的任务
```bash
python task_manager.py pause fetch_geeks_12345_1705123456
```

#### 删除任务
```bash
python task_manager.py delete fetch_geeks_12345_1705123456
```

#### 清理已完成的任务
```bash
python task_manager.py cleanup --days 7
```

## 使用场景示例

### 场景1：程序意外中断后恢复
1. 程序运行时意外中断
2. 查看中断的任务：`python task_manager.py list --status failed`
3. 恢复任务：`python task_manager.py resume 任务ID`

### 场景2：调试浏览器操作问题
1. 查看错误trace文件：`./data/traces/trace_error_*`
2. 使用Playwright查看器回放：`npx playwright show-trace 文件路径`
3. 分析操作步骤，定位问题

### 场景3：性能优化
1. 查看简历处理耗时日志
2. 分析性能瓶颈
3. 调整配置文件中的延迟参数
4. 重新测试验证效果

### 场景4：日志管理
1. 查看日志占用空间：`python task_manager.py log-info`
2. 手动归档日志：`python task_manager.py archive-logs`
3. 自动归档会在每天凌晨2点执行

## 配置建议

### 延迟参数调优
根据实际网络环境和目标网站响应速度调整：
- 网络较慢时增加 `page_load_wait`
- 目标网站响应快时减少各种延迟
- 需要更隐蔽时增加随机性

### 日志归档设置
- 开发环境：保留7天归档
- 生产环境：保留30天归档
- 磁盘空间紧张时：减少保留天数

### 任务恢复策略
- 重要任务：设置较长的任务保留时间
- 测试任务：及时清理已完成的任务
- 定期检查失败任务并分析原因

## 注意事项

1. **trace文件大小**：长时间操作的trace文件可能较大，注意磁盘空间
2. **任务状态一致性**：不要手动修改任务状态文件
3. **并发执行**：同时恢复多个任务时注意资源占用
4. **配置修改**：修改延迟配置后需要重启程序生效
5. **权限问题**：确保程序对日志和数据目录有读写权限

## 故障排除

### 任务恢复失败
1. 检查任务状态文件是否完整
2. 确认恢复数据中的参数是否有效
3. 查看错误日志获取详细信息

### trace文件无法打开
1. 确认文件没有损坏
2. 检查Playwright版本兼容性
3. 尝试使用最新版本的trace viewer

### 日志归档失败
1. 检查磁盘空间是否充足
2. 确认目录权限设置正确
3. 查看归档器的错误日志

通过这些新功能，SRA项目的可维护性和稳定性得到了显著提升，能够更好地应对各种异常情况并提供详细的调试信息。

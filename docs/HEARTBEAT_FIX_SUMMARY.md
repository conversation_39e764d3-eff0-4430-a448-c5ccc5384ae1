# celery_worker.py心跳修复总结

## 问题描述

在长时间执行`jobFilterTrigger`任务时，celery_worker.py无法正常更新worker心跳`last_update_utc`字段，导致：

1. **监控系统误报**: 认为worker已经停止工作
2. **状态不准确**: 无法正确反映worker的实际状态
3. **管理困难**: 无法准确判断worker是否正常运行

## 问题根本原因

### 原有心跳更新机制的问题

```python
# 原有问题：心跳只在任务开始和结束时更新
async def handle_task(page, task, redis):
    await update_worker_status(redis, "processing_task", task_info=task)  # 任务开始
    
    # 长时间执行的任务（如jobFilterTrigger）
    result = await handle_task_legacy(page, task, redis)  # 可能执行几分钟到几小时
    
    # 任务结束时才更新状态
    await update_worker_status(redis, "finished", task_info=task)
```

**问题**: 在任务执行期间（可能长达几小时），`last_update_utc`不会更新，导致监控系统认为worker已死。

## 修复方案

### 1. **后台心跳任务机制**

#### 新增全局变量
```python
heartbeat_task = None  # 心跳任务
current_task_info = None  # 当前执行的任务信息
```

#### 心跳启动函数
```python
async def start_heartbeat(redis: aioredis.Redis, interval: int = 30):
    """启动心跳更新任务，定期更新worker状态"""
    async def heartbeat_loop():
        while True:
            try:
                if is_task_running and current_task_info:
                    # 任务运行中，更新心跳
                    await update_worker_status(redis, "processing_task", task_info=current_task_info)
                    logger.debug(f"心跳更新: 任务 {current_task_info.get('id', 'N/A')} 正在执行")
                else:
                    # 空闲状态，更新心跳
                    await update_worker_status(redis, "idle")
                    logger.debug("心跳更新: Worker空闲状态")
                
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                logger.info("心跳任务被取消")
                break
            except Exception as e:
                logger.error(f"心跳更新异常: {e}")
                await asyncio.sleep(interval)
    
    # 启动心跳任务
    heartbeat_task = asyncio.create_task(heartbeat_loop())
    logger.info(f"心跳任务已启动，间隔: {interval}秒")
```

#### 心跳停止函数
```python
async def stop_heartbeat():
    """停止心跳更新任务"""
    global heartbeat_task
    
    if heartbeat_task and not heartbeat_task.done():
        heartbeat_task.cancel()
        try:
            await heartbeat_task
        except asyncio.CancelledError:
            pass
        logger.info("心跳任务已停止")
```

### 2. **任务信息跟踪**

#### 任务开始时设置当前任务信息
```python
async def handle_task(page: Page, task: dict, redis: aioredis.Redis):
    global current_task_info
    
    # 设置任务运行状态和当前任务信息
    is_task_running = True
    current_task_info = task.copy()  # 保存当前任务信息供心跳使用
```

#### 任务结束时清理任务信息
```python
finally:
    # 重置任务运行状态和清理当前任务信息
    is_task_running = False
    current_task_info = None
    
    # 更新worker状态为空闲
    await update_worker_status(redis, "idle")
```

### 3. **心跳任务生命周期管理**

#### Worker启动时启动心跳
```python
async def browser_manager():
    # ... 初始化代码 ...
    
    # 启动心跳任务
    await start_heartbeat(redis, interval=30)  # 30秒间隔更新心跳
    
    # 主事件循环
    try:
        while True:
            # ... 任务处理循环 ...
```

#### Worker退出时停止心跳
```python
# 在所有退出点添加心跳停止
if command == "shutdown":
    await stop_heartbeat()  # 停止心跳任务
    await playwright_instance.stop()
    return

# 在异常处理中也添加
finally:
    try:
        asyncio.run(stop_heartbeat())
    except:
        pass
```

## 修复效果

### 修复前的问题
```
时间轴: 0s -------- 300s -------- 600s -------- 900s
状态:   开始任务     (无更新)      (无更新)      任务结束
心跳:   ✅ 更新      ❌ 无更新     ❌ 无更新     ✅ 更新
```

### 修复后的效果
```
时间轴: 0s -- 30s -- 60s -- 90s -- 120s -- 150s -- 180s
状态:   开始   心跳   心跳   心跳    心跳    心跳    结束
心跳:   ✅     ✅     ✅     ✅      ✅      ✅      ✅
```

## 技术细节

### 1. **心跳间隔设置**
- **默认间隔**: 30秒
- **可配置**: 可以根据需要调整间隔
- **平衡考虑**: 既要及时更新，又要避免过于频繁

### 2. **异常处理**
- **网络异常**: 心跳更新失败时会记录日志但不中断任务
- **任务取消**: 优雅处理心跳任务的取消
- **资源清理**: 确保心跳任务在worker退出时正确停止

### 3. **状态同步**
- **任务状态**: 通过`current_task_info`同步当前任务信息
- **运行状态**: 通过`is_task_running`标志同步运行状态
- **心跳状态**: 根据运行状态决定心跳内容

## 验证方法

### 1. **运行测试脚本**
```bash
python test_heartbeat_fix.py
```

### 2. **手动验证**
```bash
# 启动worker
python src/celery_worker.py

# 在另一个终端监控Redis状态
redis-cli
> GET worker_status_xxx
> # 每30秒检查一次，确认last_update_utc在更新
```

### 3. **长任务测试**
```bash
# 发送一个jobFilterTrigger任务
# 观察在任务执行期间，last_update_utc是否每30秒更新一次
```

## 配置说明

### 心跳间隔调整
```python
# 在browser_manager函数中调整间隔
await start_heartbeat(redis, interval=60)  # 改为60秒间隔
```

### 心跳内容自定义
```python
# 可以在heartbeat_loop中添加更多状态信息
status_info = {
    "status": "processing_task",
    "task_info": current_task_info,
    "heartbeat_count": heartbeat_count,  # 心跳计数
    "memory_usage": get_memory_usage(),  # 内存使用情况
}
```

## 监控建议

### 1. **心跳监控**
- 监控`last_update_utc`字段
- 如果超过60秒未更新，发出告警
- 记录心跳更新的频率和稳定性

### 2. **任务监控**
- 监控长时间运行的任务
- 记录任务执行时间
- 分析任务性能趋势

### 3. **异常监控**
- 监控心跳更新异常
- 记录网络连接问题
- 分析worker稳定性

## 总结

### ✅ 修复成果
1. **后台心跳机制**: 实现了独立的心跳更新任务
2. **状态实时更新**: 每30秒更新一次worker状态
3. **任务信息跟踪**: 准确跟踪当前执行的任务
4. **生命周期管理**: 心跳任务与worker生命周期同步
5. **异常处理**: 完善的错误处理和资源清理

### 🎯 核心价值
- **监控准确性**: 解决了长任务期间心跳丢失的问题
- **运维友好**: 提供了准确的worker状态信息
- **系统稳定性**: 不影响原有任务执行逻辑
- **可配置性**: 支持心跳间隔的灵活配置

### 🚀 立即生效
修复已完成，重启celery_worker.py后立即生效：
- 心跳任务自动启动
- 每30秒更新worker状态
- 长任务执行期间持续更新last_update_utc
- Worker退出时自动清理心跳任务

**🎉 celery_worker.py心跳修复成功完成！现在长时间任务执行期间也能正常更新worker心跳。**

---

**修复状态**: ✅ 已完成  
**测试状态**: ✅ 可测试  
**部署状态**: ✅ 可部署  
**监控状态**: ✅ 已优化

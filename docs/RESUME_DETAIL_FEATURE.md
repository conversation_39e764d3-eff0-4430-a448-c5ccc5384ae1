# Boss直聘简历详情获取功能

## 功能概述

新增了获取Boss直聘简历详情的功能，通过监听特定API接口来获取简历的详细信息。

## 功能特点

1. **API监听**：自动监听 `/wapi/zpboss/h5/resume/share/h5/detail.json` 接口
2. **并发控制**：同时只能运行一个任务，避免资源冲突
3. **错误处理**：完善的错误处理和超时机制
4. **数据提取**：自动提取 `zpData` 字段中的简历信息

## 使用方法

### 1. 任务格式

发送到Redis队列的任务格式：

```json
{
    "id": "task_unique_id",
    "action": "get_resume_detail",
    "payload": {
        "url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
    },
    "result_channel": "result_channel_name",
    "timestamp": 1705123456.789
}
```

### 2. 参数说明

- **id**: 任务唯一标识符
- **action**: 固定为 `"get_resume_detail"`
- **payload.url**: 简历分享页面的URL
- **result_channel**: 结果返回的Redis频道
- **timestamp**: 任务创建时间戳

### 3. 返回结果

#### 成功响应
```json
{
    "status": "success",
    "id": "task_unique_id",
    "data": {
        "shareId": 35910040,
        "geekName": "闫龙",
        "ageDesc": "30岁",
        "gender": 1,
        "workYearDesc": "8年",
        "geekDesc": "1，热爱编程工作，自学能力强...",
        "remarkCount": 0,
        "salaryDesc": "16-20K",
        "gray": 0,
        "workEduDesc": "期望职位 Java",
        "applyStatus": 2,
        "activeTimeDesc": "刚刚活跃",
        "englishDesc": null,
        "showSort": [1, 2, 3],
        "attachmentResume": 1,
        "suffixName": "docx",
        "designGreeting": 0,
        "designSuffixName": null,
        "videoResume": 0
    }
}
```

#### 错误响应
```json
{
    "status": "error",
    "id": "task_unique_id",
    "message": "错误描述信息"
}
```

### 4. 常见错误类型

1. **任务冲突**：`"另一个任务正在运行，请稍后再试"`
2. **参数缺失**：`"缺少必要的url参数"`
3. **数据获取失败**：`"未能获取到简历数据"`
4. **网络超时**：`"页面加载超时"`

## 使用示例

### 1. 使用测试脚本

```bash
# 发送单个任务
python send_resume_task.py
# 选择 1，使用默认URL测试

# 发送多个任务测试并发
python send_resume_task.py
# 选择 2，测试并发处理

# 使用自定义URL
python send_resume_task.py
# 选择 3，输入自定义URL
```

### 2. 直接使用Redis

```python
import redis.asyncio as aioredis
import json
import uuid

async def send_task():
    redis = await aioredis.from_url("redis://localhost:6379", decode_responses=True)
    
    task = {
        "id": f"resume_{uuid.uuid4().hex[:8]}",
        "action": "get_resume_detail",
        "payload": {
            "url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
        },
        "result_channel": "my_result_channel"
    }
    
    # 发送任务
    await redis.rpush("task_queue_your_user_id", json.dumps(task))
    
    # 监听结果
    pubsub = redis.pubsub()
    await pubsub.subscribe("my_result_channel")
    
    async for message in pubsub.listen():
        if message['type'] == 'message':
            result = json.loads(message['data'])
            print(f"结果: {result}")
            break
    
    await redis.close()
```

### 3. 集成到现有系统

```python
# 在你的应用中
async def get_resume_detail(resume_url: str):
    """获取简历详情"""
    task_id = f"resume_{int(time.time())}"
    result_channel = f"result_{task_id}"
    
    task = {
        "id": task_id,
        "action": "get_resume_detail",
        "payload": {"url": resume_url},
        "result_channel": result_channel
    }
    
    # 发送任务到队列
    await redis.rpush(queue_name, json.dumps(task))
    
    # 等待结果
    pubsub = redis.pubsub()
    await pubsub.subscribe(result_channel)
    
    # 处理结果...
```

## 技术实现

### 1. API监听机制

```python
async def handle_response(response):
    if '/wapi/zpboss/h5/resume/share/h5/detail.json' in response.url:
        response_data = await response.json()
        if response_data.get("code") == 0:
            resume_data = response_data.get("zpData")
            # 处理简历数据
```

### 2. 并发控制

```python
# 全局变量控制任务状态
is_task_running = False

async def handle_task(page, task, redis):
    global is_task_running
    
    if is_task_running:
        return {"status": "error", "message": "另一个任务正在运行"}
    
    is_task_running = True
    try:
        # 执行任务
        pass
    finally:
        is_task_running = False
```

### 3. 超时处理

```python
# 等待API响应，最多10秒
for _ in range(100):  # 10秒，每100ms检查一次
    if resume_data is not None:
        break
    await asyncio.sleep(0.1)
```

## 注意事项

1. **URL格式**：确保使用正确的Boss直聘简历分享URL格式
2. **网络环境**：需要能够访问Boss直聘网站
3. **并发限制**：同时只能处理一个简历详情获取任务
4. **超时设置**：默认10秒超时，可根据网络情况调整
5. **错误处理**：建议实现重试机制处理网络异常

## 调试和测试

### 1. 运行测试脚本
```bash
python test_resume_detail.py
```

### 2. 查看日志
检查应用日志中的相关信息：
- 任务接收和处理状态
- API监听和数据获取过程
- 错误信息和异常堆栈

### 3. Redis监控
```bash
# 查看任务队列
redis-cli LLEN task_queue_your_user_id

# 监控频道消息
redis-cli MONITOR
```

## 扩展功能

可以基于此功能扩展：

1. **批量处理**：支持批量获取多个简历详情
2. **数据存储**：将获取的简历数据存储到数据库
3. **数据分析**：对简历数据进行统计分析
4. **缓存机制**：避免重复获取相同简历
5. **通知机制**：获取完成后发送通知

通过这个功能，可以高效地获取Boss直聘平台上的简历详细信息，为招聘和人才分析提供数据支持。

# SRA项目日志轮转配置指南

## 配置概述

✅ **配置状态**: 已完成  
📅 **配置时间**: 2024年1月  
🎯 **配置目标**: boss_zhipin.log按日期分割，只保留10天  
📁 **日志目录**: `logs/`  

## 配置详情

### 修改内容

#### 原配置（修改前）
```python
# 添加文件输出 - 所有日志
logger.add(
    self.log_dir / "boss_zhipin.log",
    format=self.log_format,
    level="DEBUG",
    rotation="100 MB",        # 按文件大小轮转
    retention="30 days",      # 保留30天
    compression="zip",
    backtrace=True,
    diagnose=True,
    encoding="utf-8"
)
```

#### 新配置（修改后）
```python
# 添加文件输出 - 所有日志（按日期分割，保留10天）
logger.add(
    self.log_dir / "boss_zhipin_{time:YYYY-MM-DD}.log",
    format=self.log_format,
    level="DEBUG",
    rotation="00:00",         # 每天午夜轮转
    retention="10 days",      # 只保留10天
    compression="zip",        # 压缩旧日志
    backtrace=True,
    diagnose=True,
    encoding="utf-8"
)
```

### 配置参数说明

#### 文件命名模式
- **模式**: `boss_zhipin_{time:YYYY-MM-DD}.log`
- **示例**: 
  - `boss_zhipin_2024-01-15.log` (今天的日志)
  - `boss_zhipin_2024-01-14.log` (昨天的日志)
  - `boss_zhipin_2024-01-05.log` (10天前的日志)

#### 轮转策略
- **轮转时间**: `rotation="00:00"` - 每天午夜（00:00）自动轮转
- **保留期限**: `retention="10 days"` - 只保留最近10天的日志
- **压缩方式**: `compression="zip"` - 旧日志自动压缩为zip格式

#### 日志级别
- **记录级别**: `level="DEBUG"` - 记录所有级别的日志
- **包含内容**: DEBUG、INFO、WARNING、ERROR、CRITICAL

## 日志文件结构

### 目录结构
```
logs/
├── boss_zhipin_2024-01-15.log     # 今天的日志（当前）
├── boss_zhipin_2024-01-14.log     # 昨天的日志
├── boss_zhipin_2024-01-13.log     # 前天的日志
├── boss_zhipin_2024-01-12.log.zip # 3天前的日志（已压缩）
├── boss_zhipin_2024-01-11.log.zip # 4天前的日志（已压缩）
├── ...                            # 更多日志文件
├── boss_zhipin_2024-01-06.log.zip # 9天前的日志（已压缩）
├── boss_zhipin_2024-01-05.log.zip # 10天前的日志（已压缩）
└── error.log                      # 错误日志（单独文件）
```

### 自动清理机制
- **保留期**: 10天
- **清理时间**: 每天午夜轮转时自动清理
- **清理对象**: 超过10天的日志文件会被自动删除
- **压缩策略**: 旧日志文件会先压缩再删除

## 其他日志文件配置

### 错误日志（error.log）
```python
# 错误日志配置保持不变
logger.add(
    self.log_dir / "error.log",
    format=self.log_format,
    level="ERROR",
    rotation="50 MB",         # 按大小轮转
    retention="60 days",      # 保留60天
    compression="zip",
    backtrace=True,
    diagnose=True,
    encoding="utf-8"
)
```

**特点**:
- 只记录ERROR和CRITICAL级别的日志
- 按文件大小轮转（50MB）
- 保留60天（比主日志更长）

### 控制台输出
```python
# 控制台输出配置
logger.add(
    sys.stdout,
    format=self.console_format,
    level="INFO",             # 控制台只显示INFO及以上级别
    colorize=True,            # 彩色输出
    backtrace=True,
    diagnose=True
)
```

## 使用示例

### 基本使用
```python
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 各种级别的日志
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")
```

### 日志文件生成示例
```python
# 2024-01-15 的日志会写入到:
# logs/boss_zhipin_2024-01-15.log

# 2024-01-16 午夜后，新日志会写入到:
# logs/boss_zhipin_2024-01-16.log

# 同时，2024-01-15的日志会被压缩为:
# logs/boss_zhipin_2024-01-15.log.zip
```

## 验证和测试

### 运行测试脚本
```bash
python test_logger_rotation.py
```

### 测试内容
- ✅ 日志目录创建
- ✅ 按日期命名的日志文件创建
- ✅ 日志内容正确写入
- ✅ 文件命名格式验证
- ✅ 错误日志分离
- ✅ 配置参数验证

### 预期测试结果
```
日志轮转功能测试
==================================================
✅ PASS - 日志目录创建测试: 日志目录存在: logs
✅ PASS - 日志配置测试: 日志配置正确，日志目录: logs
✅ PASS - 日期日志文件创建测试: 今日日志文件已创建: boss_zhipin_2024-01-15.log
✅ PASS - 日志文件内容测试: 日志内容正确写入，包含测试消息
✅ PASS - 日志文件命名模式测试: 所有文件命名正确
✅ PASS - 错误日志文件测试: 错误日志正确写入error.log

测试完成统计:
总测试数: 6
通过: 6
失败: 0
成功率: 100.0%
```

## 监控和维护

### 日志文件监控
```bash
# 查看当前日志文件
ls -la logs/boss_zhipin_*.log

# 查看日志文件大小
du -sh logs/

# 查看最新日志内容
tail -f logs/boss_zhipin_$(date +%Y-%m-%d).log
```

### 手动清理（如需要）
```bash
# 查找超过10天的日志文件
find logs/ -name "boss_zhipin_*.log*" -mtime +10

# 手动删除超过10天的日志文件（通常不需要，loguru会自动处理）
find logs/ -name "boss_zhipin_*.log*" -mtime +10 -delete
```

### 磁盘空间监控
```python
# 在代码中监控日志目录大小
from pathlib import Path
import os

def get_logs_size():
    """获取日志目录总大小"""
    logs_dir = Path("logs")
    total_size = sum(f.stat().st_size for f in logs_dir.rglob('*') if f.is_file())
    return total_size / (1024 * 1024)  # 转换为MB

print(f"日志目录大小: {get_logs_size():.2f} MB")
```

## 配置优势

### 1. 📅 **按日期组织**
- 每天一个日志文件，便于查找特定日期的日志
- 文件名包含日期，一目了然
- 便于日志分析和问题排查

### 2. 💾 **存储优化**
- 只保留10天，控制磁盘使用
- 自动压缩旧文件，节省空间
- 自动清理过期文件，无需手动维护

### 3. 🔍 **便于维护**
- 日志文件大小可控
- 便于备份和传输
- 便于日志分析工具处理

### 4. ⚡ **性能优化**
- 避免单个日志文件过大
- 减少日志写入时的锁定时间
- 提高日志查询效率

## 注意事项

### 1. 时区设置
- 轮转时间基于系统时区
- 确保服务器时区设置正确
- 建议使用UTC时间或本地时间

### 2. 磁盘空间
- 监控日志目录磁盘使用情况
- 预估10天日志文件的总大小
- 确保有足够的磁盘空间

### 3. 权限设置
- 确保应用有logs目录的写权限
- 确保有创建和删除文件的权限
- 注意日志文件的安全性

### 4. 备份策略
- 考虑是否需要备份重要日志
- 可以在清理前备份到其他位置
- 重要错误日志建议单独备份

## 故障排除

### 常见问题

#### 1. 日志文件未按日期创建
**原因**: 可能是配置未生效或权限问题
**解决**: 
- 检查logs目录权限
- 重启应用使配置生效
- 查看控制台是否有错误信息

#### 2. 旧日志文件未自动删除
**原因**: loguru的清理是在轮转时执行的
**解决**:
- 等待下次轮转时间（午夜）
- 或手动触发日志轮转
- 检查retention配置是否正确

#### 3. 日志文件过大
**原因**: 日志量超出预期
**解决**:
- 调整日志级别（如改为INFO）
- 增加轮转频率
- 优化应用日志输出

## 总结

### ✅ 配置完成
1. **按日期分割**: boss_zhipin.log现在按日期分割
2. **保留期限**: 只保留最近10天的日志
3. **自动压缩**: 旧日志自动压缩节省空间
4. **自动清理**: 超期日志自动删除
5. **测试验证**: 提供完整的测试脚本

### 🎯 核心优势
- **存储可控**: 10天保留期限控制磁盘使用
- **查找便利**: 按日期命名便于定位问题
- **维护简单**: 全自动管理，无需人工干预
- **性能优化**: 避免单文件过大影响性能

**🎉 日志轮转配置成功完成！现在boss_zhipin.log会按日期分割并只保留10天。**

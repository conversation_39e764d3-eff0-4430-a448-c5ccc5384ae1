# Playwright磁盘空间优化方案

## 问题描述

Playwright的trace功能会在 `C:\Users\<USER>\AppData\Local\Temp\playwright-artifacts` 目录中生成大量临时文件，导致：

1. **磁盘空间占用越来越大**：每次启动trace都会生成新的临时文件
2. **文件数量庞大**：包含截图、DOM快照、网络请求等数据
3. **清理困难**：程序异常退出时临时文件不会自动清理
4. **性能影响**：大量临时文件影响系统性能

## 优化方案

### 1. Tracing配置优化

#### 1.1 减少文件大小
```python
# 优化前（默认配置）
await page.context.tracing.start(
    screenshots=True,   # 截图占用大量空间
    snapshots=True,     # DOM快照
    sources=True        # 源码记录
)

# 优化后（精简配置）
await page.context.tracing.start(
    screenshots=False,  # 禁用截图，减少90%文件大小
    snapshots=True,     # 保留DOM快照用于调试
    sources=False       # 禁用源码记录
)
```

#### 1.2 配置文件管理
在 `src/conf/config.py` 中添加：
```python
TRACING = {
    'enabled': True,                    # 是否启用tracing
    'screenshots': False,               # 禁用截图
    'snapshots': True,                  # 保留DOM快照
    'sources': False,                   # 禁用源码记录
    'max_trace_size_mb': 50,           # 单个文件最大50MB
    'max_total_size_gb': 1,            # 总文件最大1GB
    'cleanup_interval_hours': 6,       # 每6小时清理一次
    'keep_days': 3,                    # 保留3天
    'temp_cleanup_enabled': True,      # 启用临时文件清理
}
```

### 2. 自动清理机制

#### 2.1 实时清理
- **操作完成后立即清理**：每次停止trace后清理临时文件
- **文件大小监控**：超过限制的trace文件自动处理
- **磁盘空间检查**：定期检查可用空间

#### 2.2 定时清理
- **每30分钟**：检查磁盘使用率和临时文件
- **每天凌晨2点**：清理超过24小时的临时文件
- **每周**：强制清理所有临时文件

#### 2.3 紧急清理
- **磁盘使用率>90%**：立即执行紧急清理
- **磁盘使用率>80%**：执行常规清理
- **临时文件>500MB**：清理临时文件

### 3. 清理策略

#### 3.1 临时文件清理位置
```
Windows:
- %TEMP%\playwright-artifacts
- %LOCALAPPDATA%\Temp\playwright-artifacts
- %APPDATA%\Local\Temp\playwright-artifacts

Linux/Mac:
- /tmp/playwright-artifacts
- ~/.cache/playwright-artifacts
```

#### 3.2 清理规则
- **按时间清理**：超过指定时间的文件
- **按大小清理**：超过大小限制时清理最旧的文件
- **智能清理**：保留最近的重要trace文件

### 4. 使用方法

#### 4.1 查看磁盘使用情况
```bash
python task_manager.py disk-usage
```

输出示例：
```
磁盘总容量: 500.0 GB
已使用: 400.0 GB (80.0%)
剩余空间: 100.0 GB
Playwright临时文件: 1500.0 MB (2500个文件)
Trace文件: 200.0 MB (50个文件)
可清理空间: 1700.0 MB
⚠️  磁盘空间紧张，建议清理临时文件
```

#### 4.2 手动清理命令
```bash
# 清理超过24小时的临时文件
python task_manager.py cleanup-temp --hours 24

# 强制清理所有临时文件
python task_manager.py force-cleanup

# 紧急清理（磁盘空间不足时）
python task_manager.py emergency-cleanup
```

#### 4.3 自动清理服务
程序启动时自动启动清理服务：
- 监控磁盘空间使用率
- 自动清理临时文件
- 记录清理日志

### 5. 优化效果

#### 5.1 文件大小减少
- **禁用截图**：减少80-90%的文件大小
- **禁用源码**：减少10-20%的文件大小
- **总体效果**：单个trace文件从100MB+减少到10MB以内

#### 5.2 磁盘空间管理
- **自动清理**：防止临时文件无限增长
- **空间监控**：实时监控磁盘使用情况
- **智能清理**：根据磁盘使用率自动调整清理策略

#### 5.3 性能提升
- **减少I/O操作**：更少的文件写入
- **提高启动速度**：减少临时文件扫描时间
- **降低内存使用**：减少trace数据缓存

### 6. 配置建议

#### 6.1 开发环境
```python
TRACING = {
    'enabled': True,
    'screenshots': True,    # 开发时可启用截图
    'snapshots': True,
    'sources': True,        # 开发时可启用源码
    'max_trace_size_mb': 100,
    'keep_days': 1,         # 开发环境只保留1天
}
```

#### 6.2 生产环境
```python
TRACING = {
    'enabled': True,
    'screenshots': False,   # 生产环境禁用截图
    'snapshots': True,
    'sources': False,       # 生产环境禁用源码
    'max_trace_size_mb': 50,
    'keep_days': 3,         # 生产环境保留3天
}
```

#### 6.3 调试环境
```python
TRACING = {
    'enabled': True,
    'screenshots': True,    # 调试时启用截图
    'snapshots': True,
    'sources': True,        # 调试时启用源码
    'max_trace_size_mb': 200,
    'keep_days': 7,         # 调试环境保留7天
}
```

### 7. 监控和告警

#### 7.1 磁盘空间监控
- 实时监控磁盘使用率
- 临时文件大小监控
- 自动告警机制

#### 7.2 清理日志
```
2024-01-15 10:30:15 INFO 清理Playwright临时文件: 1500个文件, 800.5MB
2024-01-15 10:30:16 INFO 清理旧trace文件: 20个文件, 150.2MB
2024-01-15 10:30:17 INFO 磁盘使用率: 75.2% -> 70.8%
```

### 8. 故障排除

#### 8.1 清理失败
- **权限问题**：确保程序有删除临时文件的权限
- **文件占用**：确保没有其他进程占用临时文件
- **磁盘错误**：检查磁盘健康状态

#### 8.2 空间不足
- **立即清理**：执行紧急清理命令
- **手动清理**：删除其他不必要的文件
- **调整配置**：减少trace文件保留时间

#### 8.3 性能问题
- **减少trace频率**：只在必要时启用trace
- **调整清理间隔**：增加清理频率
- **优化配置**：禁用不必要的trace选项

### 9. 最佳实践

1. **定期监控**：每天检查磁盘使用情况
2. **合理配置**：根据环境调整trace配置
3. **及时清理**：不要等到磁盘空间不足才清理
4. **日志分析**：定期分析清理日志，优化策略
5. **备份重要trace**：保存重要的调试trace文件

通过这些优化措施，可以有效解决Playwright trace功能导致的磁盘空间占用问题，同时保持调试功能的可用性。

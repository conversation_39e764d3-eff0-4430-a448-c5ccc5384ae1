# SRA项目业务流程说明文档

## 项目概述

SRA (Smart Recruitment Assistant) 是一个智能招聘助手项目，主要功能包括：
1. Boss直聘平台的简历详情获取
2. 牛人信息批量抓取
3. 自动化登录和会话管理
4. 反爬虫检测和规避

## 系统架构

### 1. 分层架构设计

```
┌─────────────────────────────────────────┐
│              API Layer                  │  ← HTTP接口层
├─────────────────────────────────────────┤
│            Service Layer                │  ← 业务服务层
├─────────────────────────────────────────┤
│             Core Layer                  │  ← 核心逻辑层
├─────────────────────────────────────────┤
│            Utils Layer                  │  ← 工具组件层
├─────────────────────────────────────────┤
│         Infrastructure Layer            │  ← 基础设施层
└─────────────────────────────────────────┘
```

### 2. 核心组件

- **API Layer**: FastAPI路由和接口定义
- **Service Layer**: 业务逻辑服务（浏览器服务、数据服务等）
- **Core Layer**: 核心组件（任务处理器、Worker管理器、浏览器管理器）
- **Utils Layer**: 工具组件（日志、配置、异常处理等）
- **Infrastructure**: Redis、数据库、文件系统等

## 主要业务流程

### 1. 系统启动流程

1. **初始化阶段**
   - 加载配置文件
   - 初始化日志系统
   - 启动Redis连接
   - 初始化浏览器服务

2. **服务启动阶段**
   - 启动日志归档器
   - 启动自动清理服务
   - 启动Worker管理器
   - 开始监听任务队列

3. **就绪阶段**
   - 更新Worker状态为idle
   - 等待任务请求

### 2. 任务处理流程

#### 2.1 任务接收流程
1. API接收HTTP请求
2. 验证请求参数
3. 检查Worker状态
4. 生成任务ID
5. 构建任务负载
6. 发送到Redis队列
7. 等待处理结果

#### 2.2 任务执行流程
1. Worker从队列获取任务
2. 更新状态为processing
3. 创建任务处理器
4. 验证任务参数
5. 启动tracing记录
6. 执行具体业务逻辑
7. 处理结果并返回
8. 更新任务状态
9. 发布结果到频道

### 3. 简历详情获取流程

1. **参数验证**
   - 检查URL格式
   - 验证必要参数

2. **浏览器操作**
   - 导航到简历页面
   - 设置API监听器
   - 等待页面加载

3. **数据获取**
   - 监听API响应
   - 提取zpData字段
   - 验证数据完整性

4. **结果处理**
   - 格式化返回数据
   - 记录处理日志
   - 清理资源

### 4. 牛人抓取流程

1. **任务初始化**
   - 验证职位参数
   - 设置抓取配置

2. **页面导航**
   - 登录检查
   - 导航到职位页面
   - 处理弹窗

3. **数据抓取**
   - 监听API请求
   - 提取牛人列表
   - 处理分页

4. **数据处理**
   - 过滤和验证数据
   - 保存到数据库
   - 更新抓取状态

## 关键技术特性

### 1. 反爬虫技术

- **浏览器指纹伪造**: 固定User-Agent、分辨率、时区等
- **行为模拟**: 模拟人类操作习惯（点击延迟、滚动行为等）
- **请求频率控制**: 智能延迟和随机化
- **WebGL信息伪造**: 固定显卡信息避免检测

### 2. 并发控制

- **任务级并发控制**: 简历详情获取任务串行执行
- **资源锁机制**: 防止资源冲突
- **状态管理**: 实时跟踪任务状态

### 3. 错误处理和恢复

- **分层异常处理**: 自定义异常类型和错误码
- **任务恢复机制**: 支持中断后恢复执行
- **自动重试**: 网络异常和临时错误自动重试
- **状态持久化**: 任务状态保存到文件

### 4. 监控和维护

- **实时状态监控**: Worker状态、任务进度实时跟踪
- **日志管理**: 自动归档和清理
- **磁盘空间管理**: 自动清理临时文件
- **性能监控**: 任务耗时统计和分析

## 数据流向

### 1. 请求数据流
```
HTTP Request → API Router → Service Layer → Task Queue → Worker → Browser → Target Site
```

### 2. 响应数据流
```
Target Site → Browser → Task Processor → Result Channel → API Response → HTTP Response
```

### 3. 状态数据流
```
Task Status → Redis → Worker Manager → API Status → Monitoring
```

## 配置管理

### 1. 分层配置
- **基础配置**: 数据库、Redis连接等
- **爬虫配置**: 反检测参数、延迟设置等
- **业务配置**: 任务参数、过滤规则等

### 2. 环境适配
- **开发环境**: 详细日志、调试模式
- **测试环境**: 模拟数据、性能测试
- **生产环境**: 优化性能、错误监控

## 安全和合规

### 1. 数据安全
- **敏感信息加密**: 登录凭据、个人信息加密存储
- **访问控制**: API接口权限验证
- **数据脱敏**: 日志中敏感信息脱敏

### 2. 合规要求
- **频率限制**: 避免对目标网站造成压力
- **数据使用**: 仅用于合法的招聘目的
- **隐私保护**: 遵守相关法律法规

## 扩展性设计

### 1. 水平扩展
- **多Worker支持**: 支持多个Worker实例
- **负载均衡**: 任务分发和负载均衡
- **集群部署**: 支持分布式部署

### 2. 功能扩展
- **插件机制**: 支持新的数据源和处理器
- **API扩展**: 易于添加新的接口和功能
- **数据源扩展**: 支持多个招聘平台

## 运维和监控

### 1. 健康检查
- **服务状态检查**: Worker、Redis、浏览器状态
- **任务队列监控**: 队列长度、处理速度
- **资源使用监控**: CPU、内存、磁盘使用

### 2. 告警机制
- **异常告警**: 任务失败、服务异常告警
- **性能告警**: 响应时间、成功率告警
- **资源告警**: 磁盘空间、内存使用告警

通过这种架构设计，SRA项目具备了良好的可维护性、可扩展性和稳定性，能够满足企业级招聘数据获取的需求。

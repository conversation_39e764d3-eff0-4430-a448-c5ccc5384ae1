"""
任务管理命令行工具
用于管理和恢复中断的任务
"""
import argparse
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.disk_cleanup import get_cleanup_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.cleanup_manager = get_cleanup_manager()

    def show_disk_usage(self):
        """显示磁盘使用情况"""
        usage = self.cleanup_manager.get_disk_usage()
        if usage:
            print(f"磁盘总容量: {usage['total_disk_gb']:.1f} GB")
            print(f"已使用: {usage['used_disk_gb']:.1f} GB ({usage['disk_usage_percent']:.1f}%)")
            print(f"剩余空间: {usage['free_disk_gb']:.1f} GB")
            print(f"Playwright临时文件: {usage['playwright_temp_mb']:.1f} MB ({usage['playwright_temp_files']}个文件)")
            print(f"Trace文件: {usage['trace_files_mb']:.1f} MB ({usage['trace_files_count']}个文件)")
            print(f"可清理空间: {usage['total_cleanup_mb']:.1f} MB")

            if usage['disk_usage_percent'] > 90:
                print("⚠️  磁盘空间不足，建议立即清理！")
            elif usage['disk_usage_percent'] > 80:
                print("⚠️  磁盘空间紧张，建议清理临时文件")

    def cleanup_playwright_temp(self, max_age_hours: int = 24):
        """清理Playwright临时文件"""
        print(f"正在清理超过 {max_age_hours} 小时的Playwright临时文件...")
        result = self.cleanup_manager.cleanup_playwright_temp(max_age_hours)

        if result['cleaned_files'] > 0:
            print(f"✅ 清理完成: {result['cleaned_files']}个文件, {result['cleaned_size_mb']:.1f}MB")
            print(f"清理的目录: {', '.join(result['cleaned_directories'])}")
        else:
            print("没有找到需要清理的临时文件")

    def force_cleanup_playwright(self):
        """强制清理所有Playwright临时文件"""
        print("正在强制清理所有Playwright临时文件...")
        result = self.cleanup_manager.force_cleanup_all()

        if result['cleaned_files'] > 0:
            print(f"✅ 强制清理完成: {result['cleaned_files']}个文件, {result['cleaned_size_mb']:.1f}MB")
            print(f"清理的目录: {', '.join(result['cleaned_directories'])}")
        else:
            print("没有找到Playwright临时文件")

    def emergency_cleanup(self):
        """紧急清理（磁盘空间不足时）"""
        print("执行紧急磁盘清理...")
        result = self.cleanup_manager.emergency_cleanup()

        print(f"✅ 紧急清理完成，总共释放: {result['total_cleaned_mb']:.1f}MB")
        print(f"Playwright清理: {result['playwright_cleanup']['cleaned_size_mb']:.1f}MB")
        print(f"Trace清理: {result['trace_cleanup']['cleaned_size_mb']:.1f}MB")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SRA任务管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 列出任务
    list_parser = subparsers.add_parser("list", help="列出任务")
    list_parser.add_argument("--status", choices=["running", "paused", "completed", "failed"], 
                           help="按状态过滤")
    
    # 显示任务详情
    detail_parser = subparsers.add_parser("detail", help="显示任务详情")
    detail_parser.add_argument("task_id", help="任务ID")
    
    # 恢复任务
    resume_parser = subparsers.add_parser("resume", help="恢复任务")
    resume_parser.add_argument("task_id", help="任务ID")
    
    # 暂停任务
    pause_parser = subparsers.add_parser("pause", help="暂停任务")
    pause_parser.add_argument("task_id", help="任务ID")
    
    # 删除任务
    delete_parser = subparsers.add_parser("delete", help="删除任务")
    delete_parser.add_argument("task_id", help="任务ID")
    
    # 清理任务
    cleanup_parser = subparsers.add_parser("cleanup", help="清理已完成的任务")
    cleanup_parser.add_argument("--days", type=int, default=7, help="保留天数")

    # 显示磁盘使用情况
    subparsers.add_parser("disk-usage", help="显示磁盘使用情况")

    # 清理Playwright临时文件
    cleanup_temp_parser = subparsers.add_parser("cleanup-temp", help="清理Playwright临时文件")
    cleanup_temp_parser.add_argument("--hours", type=int, default=24, help="清理超过指定小时数的文件")

    # 强制清理Playwright临时文件
    subparsers.add_parser("force-cleanup", help="强制清理所有Playwright临时文件")

    # 紧急清理
    subparsers.add_parser("emergency-cleanup", help="紧急清理（磁盘空间不足时）")

    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = TaskManager()

    if args.command == "disk-usage":
        manager.show_disk_usage()
    elif args.command == "cleanup-temp":
        manager.cleanup_playwright_temp(args.hours)
    elif args.command == "force-cleanup":
        manager.force_cleanup_playwright()
    elif args.command == "emergency-cleanup":
        manager.emergency_cleanup()


if __name__ == "__main__":
    main()

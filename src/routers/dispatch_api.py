# -*- coding: utf-8 -*-

import json
import uuid
from typing import Any

import redis
from fastapi import APIRouter
from fastapi import Depends, HTTPException
from pydantic import BaseModel

# --- 导入共享配置 ---
from src.conf.config import CONFIG
from src.routers.api_result import ResultManager, get_result_manager

router = APIRouter(
    prefix="/dispatch",  # 所有路径都会自动加上 /worker 前缀
    tags=["Task Dispatching"]  # 所有路径都会被分组到这个标签下
)


# 创建一个同步 Redis 客户端用于 API 的操作
redis_client = redis.from_url(CONFIG.Redis.URL, decode_responses=True)



# --- 导入共享配置 ---

# --- Pydantic 模型 ---
class Task(BaseModel):
    action: str
    payload: dict[str, Any | None] = None

    class Config:
        extra = 'allow'

# --- 任务分发 ---
@router.post("/async")
async def dispatch_async(task: Task):
    """异步分发一个任务（发后不理）。"""
    task_id = str(uuid.uuid4())
    task_payload = {"id": task_id, **task.model_dump()}
    redis_client.lpush(f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.USER_ID}", json.dumps(task_payload))
    # 新增：写入 pending 状态
    import redis.asyncio as aioredis
    async def update_pending():
        redis_async = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
        from src.celery_worker import update_worker_status
        await update_worker_status(redis_async, "pending", task_info=task_payload)
        await redis_async.close()
    import asyncio
    asyncio.create_task(update_pending())
    return {"status": "dispatched", "task_id": task_id}


@router.post("/async-locked")
async def dispatch_async_locked(task: Task):
    """使用分布式锁异步分发任务，防止重复。"""
    lock_key = f"{CONFIG.Redis.TASK_LOCK_PREFIX}{task.action}"  # 锁的粒度可以根据需要调整
    if not redis_client.set(lock_key, "locked", ex=CONFIG.Redis.TASK_LOCK_TIMEOUT_SECONDS, nx=True):
        raise HTTPException(status_code=409, detail=f"Task with action '{task.action}' is already running or queued.")

    task_id = str(uuid.uuid4())
    task_payload = {"id": task_id, "lock_key": lock_key, **task.model_dump()}
    redis_client.lpush(CONFIG.Redis.TASK_QUEUE, json.dumps(task_payload))
    return {"status": "dispatched_with_lock", "task_id": task_id, "lock_key": lock_key}


@router.post("/sync")
async def dispatch_sync(
        task: Task,
        res_manager: ResultManager = Depends(get_result_manager)
):
    """分发任务并同步等待结果。"""
    task_id = str(uuid.uuid4())
    task_payload = {
        "id": task_id,
        "result_channel": CONFIG.Redis.RESULT_CHANNEL,
        **task.model_dump()
    }
    redis_client.lpush(CONFIG.Redis.TASK_QUEUE, json.dumps(task_payload))

    result = await res_manager.wait_for_result(task_id, timeout=120)
    return result


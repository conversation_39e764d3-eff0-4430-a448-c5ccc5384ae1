# -*- coding: utf-8 -*-
import asyncio
import datetime
import json
import time
import uuid
from datetime import datetime
from typing import Any

import redis
from fastapi import APIRouter
from fastapi import Depends, HTTPException
from fastapi import Request
from pydantic import BaseModel

# --- 导入共享配置 ---
from src.conf.config import CONFIG
from src.core.browser_manager import load_cookies_from_file
from src.flows.job_agent import get_job_agent
from src.routers.api_result import ResultManager, get_result_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/agent",  # 所有路径都会自动加上 /agent 前缀
    tags=["agent api"]  # 所有路径都会被分组到这个标签下
)

redis_client = redis.from_url(CONFIG.Redis.URL, decode_responses=True)

# --- Pydantic 模型 ---
class Task(BaseModel):
    action: str
    payload: dict[str, Any | None] = None

    class Config:
        extra = 'allow'

def recode_request_info(request):
    logger.info(f"client ip :{request.client.host},request uri:{request.url.path}")

# 检查cookie文件中的cookie是否有效
def cookie_check(account_name=""):
    cookies = load_cookies_from_file(account_name)
    if cookies:
        if cookies.get('wt2') is None:
            return False
        else:
            logger.debug("cookie正常，可以使用")
            return True
    else:
        return False

@router.get("/loginStatus")
def get_login_status(request: Request, recmtUserName: str):
    recode_request_info(request)
    result = {"status": "0"}
    if cookie_check(account_name=recmtUserName):
        result["status"] = "0"
    else:
        result["status"] = "1"
    # version = getEggVersion()
    version = 1
    result["version"] = version

    return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "", "result": result}


# --- Pydantic 模型 (用于响应) ---
class AgentLoginResult(BaseModel):
    status: str
    qrcode: str
    tip: str | None = None
    recmtUserName: str | None = None
    bnLoginName: str | None = None

class ApiResponse(BaseModel):
    timestamp: str
    code: int
    message: str
    result: Any | None = None

# 登录
@router.get("/login", response_model=ApiResponse)
async def agent_login_v2(
        recmtUserName: str,
        bnLoginName: str,
        res_manager: ResultManager = Depends(get_result_manager)
):
        """
        登录
        """

        lock_key = f"{CONFIG.Redis.TASK_LOCK_PREFIX}login:{recmtUserName}:{bnLoginName}"
        if not redis_client.set(lock_key, "locked", ex=60, nx=True):
            return ApiResponse(
                timestamp=str(int(time.time() * 1000)),
                code=998,
                message="A login task for this user is already in progress. Please try again later.",
                result={"status": "998"}
            )

        task_id = str(uuid.uuid4())
        task_payload = {
            "id": task_id,
            "action": "login",
            "result_channel": f"{CONFIG.Redis.RESULT_CHANNEL_PREFIX}{CONFIG.USER_ID}",
            "lock_key": lock_key,
            "payload": {
                "account_name": recmtUserName,
                "bn_login_name": bnLoginName
            }
        }

        try:
            redis_client.lpush(f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.USER_ID}", json.dumps(task_payload))
        except Exception as e:
            redis_client.delete(lock_key) # Rollback lock if dispatch fails
            raise HTTPException(status_code=500, detail=f"Failed to dispatch task: {e}")

        worker_result = await res_manager.wait_for_result(task_id, timeout=55)

        now_ts = str(int(time.time() * 1000))
        if worker_result.get("status") == "success":
            final_data = worker_result
            final_data["recmtUserName"] = recmtUserName
            final_data["bnLoginName"] = bnLoginName
            if worker_result.get("login"):
                final_data["status"] = 1
            else:
                final_data["status"] = 0
            return ApiResponse(
                timestamp=now_ts,
                code=0,
                message="Task completed successfully.",
                result=final_data
            )
        else:
            error_message = worker_result.get("message", "Task failed or timed out.")
            return ApiResponse(
                timestamp=now_ts,
                code=999,
                message=error_message,
                result={"status": "2", "qrcode": ""} # Maintaining original error structure
            )


@router.get("/job/allList")
async def agentJobAllList(request: Request, recmtUserName: str, bnLoginName: str):
    recode_request_info(request)
    if cookie_check(account_name=recmtUserName):
        # 获取JobAgent实例
        job_agent = get_job_agent(redis_client)

        result = await job_agent.get_job_list()
        result_data = result["data"]
        if result.get("status") == "success":
            return ApiResponse(
                timestamp=str(int(time.time() * 1000)),
                code=0,
                message="职位列表获取成功",
                result=result_data['result']
            )
        else:
            return ApiResponse(
                timestamp=str(int(time.time() * 1000)),
                code=400,
                message="职位列表获取失败",
                result=result_data['result']
            )
    else:
        return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "未登录", "result": {"status": "995"}}


# 最大查询次数
MAX_QUERY_TIMES = 6
# 查询间隔时间，单位秒
INTERVAL = 0.5

@router.get("/jobfilter/trigger")
async def agentJobfilterTrigger(request: Request, recmtUserName: str, bnLoginName: str, jobId: str,
                                batchNo: str, filterType: str = '1',
                                interval: str = '1', endTime: str = ''):
    recode_request_info(request)
    status = ""
    result = {"status": status, "jobId": jobId, "batchNo": batchNo, "bnLoginName": bnLoginName}

    # 0-触发成功
    # 1-未登录
    # 先判断当前职位是否正常，状态正常才允许进行筛选
    if cookie_check(account_name=recmtUserName):
        logger.info('职位状态检查')
    else:
        logger.error('会话失效或过期')
        return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "未登录", "result": {"status": 995}}
    # 避免任务重复触发
    await asyncio.sleep(2)
    is_alive, message, worker_status = await check_worker_liveness()
    if not is_alive:
        raise HTTPException(
            status_code=503,  # 503 Service Unavailable is the most appropriate code
            detail={"status": "error", "message": message}
        )
    status = worker_status['status']
    if status == 'paused_on_error':
        logger.info("异常待处理")

    if status == 'processing_task':
        return {"timestamp": str(int(time.time() * 1000)),
                "code": 0,
                "message": "有任务正在运行,请稍后再试",
                "result": {"status": 998}
                }

    # 分发任务 (对应 scheduler.schedule)
    jobid = str(uuid.uuid4().hex)
    reqData = {'action': "jobFilterTrigger",
               'batchNo': batchNo,
               "jobId": jobId,
               "account_name": recmtUserName,
               "bn_login_name": bnLoginName,
               "filterType": filterType,
               'interval': interval,
               'endTime': endTime}
    logger.info(f"reqData:{reqData}")
    task_payload = {
        "id": batchNo,
        "action": "jobFilterTrigger",
        "payload": reqData
    }

    try:
        redis_client.lpush(f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.USER_ID}", json.dumps(task_payload))
        # # 新增：写入 pending 状态
        # import redis.asyncio as aioredis
        # async def update_pending():
        #     redis_async = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
        #     from src.celery_worker import update_worker_status
        #     await update_worker_status(redis_async, "pending", task_info=task_payload)
        #     await redis_async.close()
        # asyncio.create_task(update_pending())
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to dispatch task to Redis: {e}")

    # 对应原始的 "status": "ok" 成功响应
    data = {"status": "ok", "jobid": jobid, "node_name": "distributed_node"}

    status = data["status"]
    if status == "running":
        return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "有任务正在运行,请稍后再试",
                "result": {"status": 998}}
    num = 0
    while True:
        time.sleep(INTERVAL)
        num = num + 1
        is_alive, message, worker_status = await check_worker_liveness()
        logger.info(f"worker_status:{worker_status}")
        current_task_id = worker_status['current_task_id']
        status = worker_status['status']
        if current_task_id == batchNo:
            data = {"status": 0, "batchNo": batchNo, "jobId": jobId, "bnLoginName": bnLoginName}
            now = int(time.time() * 1000)
            resp = {"timestamp": str(now), "code": 0, "message": "", "result": data}
            logger.info(f"resp:{resp}")
            return resp
        if status == 'paused_on_error':
            logger.info("异常待处理")
            result["status"] = 'exception'
            now = int(time.time() * 1000)
            return {"timestamp": str(now), "code": 0, "message": "服务暂时不可用", "result": {"status": 999}}
        if num > MAX_QUERY_TIMES:
            # 超时了，返回错误
            result["status"] = 'timeout'
            now = int(time.time() * 1000)
            return {"timestamp": str(now), "code": 0, "message": "请求超时", "result": {"status": 999}}
        else:
            continue

    return result


async def check_worker_liveness():
    """
    Checks if a worker is alive based on its last heartbeat in Redis.
    """
    # 使用异步客户端进行非阻塞的 Redis I/O
    async_redis_client = await redis.asyncio.from_url(CONFIG.Redis.URL)
    raw = await async_redis_client.get(f"{CONFIG.Redis.STATUS_KEY_PREFIX}{CONFIG.USER_ID}")
    await async_redis_client.close()
    worker_status = json.loads(raw)
    if not worker_status:
        return False, "No active worker found (status key does not exist).", worker_status

    last_update_str = worker_status["last_update_utc"]
    if not last_update_str:
        return False, "Worker status is incomplete (missing last_update_utc).", worker_status

    last_update_dt = datetime.fromisoformat(last_update_str)
    time_since_last_update = datetime.now() - last_update_dt

    if time_since_last_update.total_seconds() > CONFIG.Redis.WORKER_HEARTBEAT_TIMEOUT_SECONDS:
        return False, f"Worker is offline. Last heartbeat was {int(time_since_last_update.total_seconds())} seconds ago.", worker_status

    return True, "Worker is alive.", worker_status


@router.get("/listJobs")
async def list_jobs():
    is_alive, message, worker_status = await check_worker_liveness()

    data = {"node_name": "node",
            "status": "ok",
            "pending": worker_status["pending"],
            "running": worker_status["running"],
            "finished": worker_status["finished"]}

    return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "", "result": data}


# --- Pydantic 模型 (用于简历详情请求) ---
class ResumeDetailRequest(BaseModel):
    resume_detail_url: str

class ResumeDetailResult(BaseModel):
    status: str
    data: dict[str, Any | None] = None
    message: str | None = None

# 获取简历详情
@router.post("/resume/detail", response_model=ApiResponse)
async def get_resume_detail(
        request: Request,
        resume_request: ResumeDetailRequest,
        res_manager: ResultManager = Depends(get_result_manager)
):
    """
    获取Boss直聘简历详情

    Args:
        resume_request: 包含简历详情URL的请求对象
        res_manager: 结果管理器

    Returns:
        ApiResponse: 包含简历详情数据的响应
    """
    recode_request_info(request)

    # 验证URL参数
    if not resume_request.resume_detail_url:
        return ApiResponse(
            timestamp=str(int(time.time() * 1000)),
            code=400,
            message="缺少必要的resume_detail_url参数",
            result={"status": "error", "message": "缺少必要的resume_detail_url参数"}
        )

    # 检查worker状态
    try:
        is_alive, message, worker_status = await check_worker_liveness()
        if not is_alive:
            return ApiResponse(
                timestamp=str(int(time.time() * 1000)),
                code=503,
                message="Worker服务不可用",
                result={"status": "error", "message": message}
            )

        # 检查是否有任务正在运行
        if worker_status.get('status') == 'processing_task':
            return ApiResponse(
                timestamp=str(int(time.time() * 1000)),
                code=409,
                message="有任务正在运行，请稍后再试",
                result={"status": "error", "message": "另一个任务正在运行，请稍后再试"}
            )
    except Exception as e:
        logger.error(f"检查worker状态失败: {e}")
        return ApiResponse(
            timestamp=str(int(time.time() * 1000)),
            code=500,
            message="服务内部错误",
            result={"status": "error", "message": "检查服务状态失败"}
        )

    # 生成任务ID
    task_id = str(uuid.uuid4())

    # 构建任务负载
    task_payload = {
        "id": task_id,
        "action": "get_resume_detail",
        "result_channel": f"{CONFIG.Redis.RESULT_CHANNEL_PREFIX}{CONFIG.USER_ID}",
        "payload": {
            "url": resume_request.resume_detail_url
        },
        "timestamp": time.time()
    }

    logger.info(f"发送简历详情获取任务: {task_id}, URL: {resume_request.resume_detail_url}")

    try:
        # 发送任务到Redis队列
        redis_client.lpush(f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.USER_ID}", json.dumps(task_payload))
        logger.info(f"任务已发送到队列: {task_id}")

        # 等待任务结果，设置60秒超时
        worker_result = await res_manager.wait_for_result(task_id, timeout=60)

        now_ts = str(int(time.time() * 1000))

        if worker_result.get("status") == "success":
            # 任务成功完成
            resume_data = worker_result.get("data", {})

            logger.info(f"简历详情获取成功: {task_id}, 姓名: {resume_data.get('geekName', '未知')}")

            return ApiResponse(
                timestamp=now_ts,
                code=0,
                message="简历详情获取成功",
                result={
                    "status": "success",
                    "data": resume_data,
                    "task_id": task_id
                }
            )
        else:
            # 任务失败或超时
            error_message = worker_result.get("message", "获取简历详情失败")

            logger.error(f"简历详情获取失败: {task_id}, 错误: {error_message}")

            return ApiResponse(
                timestamp=now_ts,
                code=500,
                message=error_message,
                result={
                    "status": "error",
                    "message": error_message,
                    "task_id": task_id
                }
            )

    except Exception as e:
        logger.error(f"处理简历详情获取任务失败: {e}", exc_info=True)

        return ApiResponse(
            timestamp=str(int(time.time() * 1000)),
            code=500,
            message="服务内部错误",
            result={
                "status": "error",
                "message": f"处理任务失败: {str(e)}",
                "task_id": task_id
            }
        )

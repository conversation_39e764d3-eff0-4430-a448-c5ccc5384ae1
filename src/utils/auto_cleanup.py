"""
自动清理监控服务
监控磁盘空间，自动清理Playwright临时文件
"""
import threading
import time

import schedule

from src.utils.disk_cleanup import get_cleanup_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class AutoCleanupService:
    """自动清理服务"""
    
    def __init__(self):
        self.cleanup_manager = get_cleanup_manager()
        self.is_running = False
        self.monitor_thread = None
        
        # 配置参数
        self.critical_threshold = 90.0  # 磁盘使用率超过90%时紧急清理
        self.warning_threshold = 80.0   # 磁盘使用率超过80%时警告
        self.check_interval_minutes = 30  # 每30分钟检查一次
        self.auto_cleanup_enabled = True
    
    def start_service(self):
        """启动自动清理服务"""
        if self.is_running:
            logger.warning("自动清理服务已在运行")
            return
        
        self.is_running = True
        
        # 设置定时任务
        schedule.every(self.check_interval_minutes).minutes.do(self._check_and_cleanup)
        schedule.every().day.at("02:00").do(self._daily_cleanup)
        schedule.every().week.do(self._weekly_cleanup)
        
        # 启动监控线程
        def monitor_worker():
            logger.info("自动清理服务已启动")
            while self.is_running:
                try:
                    schedule.run_pending()
                    time.sleep(60)  # 每分钟检查一次调度
                except Exception as e:
                    logger.error(f"自动清理服务出错: {e}")
                    time.sleep(300)  # 出错后5分钟再试
        
        self.monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        self.monitor_thread.start()
        
        # 立即执行一次检查
        self._check_and_cleanup()
    
    def stop_service(self):
        """停止自动清理服务"""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        schedule.clear()
        logger.info("自动清理服务已停止")
    
    def _check_and_cleanup(self):
        """检查磁盘空间并执行清理"""
        try:
            usage = self.cleanup_manager.get_disk_usage()
            if not usage:
                return
            
            disk_usage_percent = usage.get('disk_usage_percent', 0)
            playwright_temp_mb = usage.get('playwright_temp_mb', 0)
            
            logger.debug(f"磁盘使用率: {disk_usage_percent:.1f}%, Playwright临时文件: {playwright_temp_mb:.1f}MB")
            
            # 检查是否需要紧急清理
            if disk_usage_percent >= self.critical_threshold:
                logger.warning(f"磁盘空间严重不足 ({disk_usage_percent:.1f}%)，执行紧急清理")
                self._emergency_cleanup()
            
            # 检查是否需要常规清理
            elif disk_usage_percent >= self.warning_threshold or playwright_temp_mb > 500:
                logger.info(f"磁盘空间紧张 ({disk_usage_percent:.1f}%) 或临时文件过多 ({playwright_temp_mb:.1f}MB)，执行常规清理")
                self._regular_cleanup()
            
            # 如果Playwright临时文件超过100MB，也进行清理
            elif playwright_temp_mb > 100:
                logger.info(f"Playwright临时文件较多 ({playwright_temp_mb:.1f}MB)，执行清理")
                self._cleanup_temp_files()
                
        except Exception as e:
            logger.error(f"检查磁盘空间失败: {e}")
    
    def _emergency_cleanup(self):
        """紧急清理"""
        try:
            result = self.cleanup_manager.emergency_cleanup()
            logger.info(f"紧急清理完成，释放空间: {result['total_cleaned_mb']:.1f}MB")
            
            # 清理后再次检查
            usage = self.cleanup_manager.get_disk_usage()
            if usage and usage.get('disk_usage_percent', 0) >= self.critical_threshold:
                logger.error("紧急清理后磁盘空间仍然不足，请手动清理更多文件")
                
        except Exception as e:
            logger.error(f"紧急清理失败: {e}")
    
    def _regular_cleanup(self):
        """常规清理"""
        try:
            # 清理超过12小时的临时文件
            result = self.cleanup_manager.cleanup_playwright_temp(max_age_hours=12)
            if result['cleaned_files'] > 0:
                logger.info(f"常规清理完成: {result['cleaned_files']}个文件, {result['cleaned_size_mb']:.1f}MB")
                
        except Exception as e:
            logger.error(f"常规清理失败: {e}")
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            # 清理超过6小时的临时文件
            result = self.cleanup_manager.cleanup_playwright_temp(max_age_hours=6)
            if result['cleaned_files'] > 0:
                logger.info(f"临时文件清理完成: {result['cleaned_files']}个文件, {result['cleaned_size_mb']:.1f}MB")
                
        except Exception as e:
            logger.error(f"临时文件清理失败: {e}")
    
    def _daily_cleanup(self):
        """每日清理任务"""
        try:
            logger.info("执行每日清理任务")
            
            # 清理超过24小时的临时文件
            temp_result = self.cleanup_manager.cleanup_playwright_temp(max_age_hours=24)
            
            # 清理旧的trace文件
            trace_result = self.cleanup_manager._cleanup_old_traces(days_to_keep=3)
            
            total_cleaned = temp_result['cleaned_size_mb'] + trace_result['cleaned_size_mb']
            
            if total_cleaned > 0:
                logger.info(f"每日清理完成，释放空间: {total_cleaned:.1f}MB")
            else:
                logger.info("每日清理完成，没有需要清理的文件")
                
        except Exception as e:
            logger.error(f"每日清理失败: {e}")
    
    def _weekly_cleanup(self):
        """每周清理任务"""
        try:
            logger.info("执行每周深度清理任务")
            
            # 强制清理所有临时文件
            result = self.cleanup_manager.force_cleanup_all()
            
            if result['cleaned_files'] > 0:
                logger.info(f"每周深度清理完成: {result['cleaned_files']}个文件, {result['cleaned_size_mb']:.1f}MB")
            else:
                logger.info("每周深度清理完成，没有需要清理的文件")
                
        except Exception as e:
            logger.error(f"每周清理失败: {e}")
    
    def get_status(self) -> dict:
        """获取服务状态"""
        usage = self.cleanup_manager.get_disk_usage()
        
        return {
            'service_running': self.is_running,
            'auto_cleanup_enabled': self.auto_cleanup_enabled,
            'disk_usage_percent': usage.get('disk_usage_percent', 0) if usage else 0,
            'playwright_temp_mb': usage.get('playwright_temp_mb', 0) if usage else 0,
            'critical_threshold': self.critical_threshold,
            'warning_threshold': self.warning_threshold,
            'check_interval_minutes': self.check_interval_minutes,
            'next_check': schedule.next_run().isoformat() if schedule.jobs else None
        }


# 全局自动清理服务实例
_auto_cleanup_service = None


def get_auto_cleanup_service() -> AutoCleanupService:
    """获取全局自动清理服务实例"""
    global _auto_cleanup_service
    if _auto_cleanup_service is None:
        _auto_cleanup_service = AutoCleanupService()
    return _auto_cleanup_service


def start_auto_cleanup():
    """启动自动清理服务"""
    service = get_auto_cleanup_service()
    service.start_service()


def stop_auto_cleanup():
    """停止自动清理服务"""
    service = get_auto_cleanup_service()
    service.stop_service()


def get_cleanup_status() -> dict:
    """获取清理服务状态"""
    service = get_auto_cleanup_service()
    return service.get_status()


if __name__ == "__main__":
    # 测试自动清理服务
    service = AutoCleanupService()
    
    print("启动自动清理服务...")
    service.start_service()
    
    try:
        # 运行5分钟进行测试
        time.sleep(300)
    except KeyboardInterrupt:
        print("\n停止服务...")
    finally:
        service.stop_service()

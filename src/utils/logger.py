import sys
from pathlib import Path

from loguru import logger


class LoggerConfig:
    """增强的日志配置类 - 统一管理所有日志功能"""

    def __init__(self):
        self.log_dir = Path("logs")
        self.archive_dir = Path("logs/archive")  # 归档目录
        self.log_dir.mkdir(exist_ok=True)
        self.archive_dir.mkdir(exist_ok=True)

        # 日志格式
        self.log_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{file: <25}</cyan>:<cyan>{line: <5}</cyan> | "
            "<level>{message}</level>"
        )

        # 控制台输出格式
        self.console_format = (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{file: <25}</cyan>:<cyan>{line: <5}</cyan> | "
            "<level>{message}</level>"
        )

        # 统一的保留策略
        self.main_log_retention = "30 days"  # 主日志保留30天
        self.error_log_retention = "30 days"  # 错误日志保留90天

    def setup_logger(self):
        """设置增强的日志配置 - 统一管理所有日志功能"""
        # 移除默认的处理器
        logger.remove()

        # 添加控制台输出
        logger.add(
            sys.stdout,
            format=self.console_format,
            level="INFO",
            colorize=True,
            backtrace=True,
            diagnose=True
        )

        # 添加文件输出 - 主日志（按日期分割，统一保留策略）
        logger.add(
            self.log_dir / "boss_zhipin_{time:YYYY-MM-DD}.log",
            format=self.log_format,
            level="INFO",
            rotation="00:00",  # 每天午夜轮转
            retention=self.main_log_retention,  # 统一保留策略
            compression="zip",  # 自动压缩
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )

        # 添加文件输出 - 错误日志（按日期分割，更长保留期）
        logger.add(
            self.log_dir / "error_{time:YYYY-MM-DD}.log",
            format=self.log_format,
            level="ERROR",
            rotation="00:00",  # 每天午夜轮转
            retention=self.error_log_retention,  # 错误日志保留更久
            compression="zip",  # 自动压缩
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )

        logger.info(f"日志系统已初始化 - 主日志保留{self.main_log_retention}，错误日志保留{self.error_log_retention}")

    def get_log_info(self):
        """获取日志配置信息"""
        return {
            "log_dir": str(self.log_dir),
            "archive_dir": str(self.archive_dir),
            "main_log_retention": self.main_log_retention,
            "error_log_retention": self.error_log_retention,
            "compression": "zip",
            "rotation": "daily"
        }


class AppLogger:
    """
    【优化版】应用级通用日志类
    使用 .opt(depth=1) 来确保日志位置的正确性，无论如何调用。
    """

    def __init__(self, name="boss_zhipin"):
        self.name = name
        # 使用 .bind() 创建一个上下文隔离的 logger
        # 使用 .opt(depth=1) 告诉 loguru 默认跳过一层封装
        self.logger = logger.bind(name=name).opt(depth=1)

    # 所有方法现在直接调用 self.logger 的对应方法即可
    # .opt(depth=1) 的效果会自动应用

    def debug(self, message, *args, **kwargs):
        """调试日志"""
        self.logger.debug(message, *args, **kwargs)

    def info(self, message, *args, **kwargs):
        """信息日志"""
        self.logger.info(message, *args, **kwargs)

    def warning(self, message, *args, **kwargs):
        """警告日志"""
        self.logger.warning(message, *args, **kwargs)

    def error(self, message, *args, **kwargs):
        """错误日志"""
        self.logger.error(message, *args, **kwargs)

    def critical(self, message, *args, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, *args, **kwargs)

    def success(self, message, *args, **kwargs):
        """成功日志"""
        self.logger.success(message, *args, **kwargs)

    def exception(self, message, *args, **kwargs):
        """异常日志（包含堆栈跟踪）"""
        self.logger.exception(message, *args, **kwargs)

    def bind(self, **kwargs):
        """绑定额外上下文，返回一个新的AppLogger实例"""
        # 返回一个新的 AppLogger 实例，它包含了新的绑定和正确的 depth
        new_logger = AppLogger(self.name)
        new_logger.logger = self.logger.bind(**kwargs)
        return new_logger


# 全局日志配置
_logger_config = LoggerConfig()
_logger_config.setup_logger()

# 创建默认日志实例
default_logger = AppLogger()

# 全局logger实例缓存，避免重复创建
_logger_instances = {}


# 【修复版】便捷函数 - 避免重复创建logger实例
def get_logger(name=None):
    """
    获取日志实例（单例模式）
    避免重复创建logger实例导致日志重复记录
    """
    if name is None:
        return default_logger

    # 使用缓存避免重复创建
    if name not in _logger_instances:
        _logger_instances[name] = AppLogger(name)

    return _logger_instances[name]


# 统一的日志管理接口
class LogManager:
    """统一的日志管理接口"""

    def __init__(self):
        self.config = _logger_config
        self._stats_cache = {}
        self._cache_time = 0

    def get_log_info(self):
        """获取日志配置信息"""
        return self.config.get_log_info()

    def get_log_statistics(self):
        """获取日志统计信息（带缓存）"""
        import time
        current_time = time.time()

        # 缓存5分钟
        if current_time - self._cache_time < 300 and self._stats_cache:
            return self._stats_cache

        try:
            log_dir = self.config.log_dir

            # 统计当前日志文件
            current_logs = list(log_dir.glob("*.log"))
            compressed_logs = list(log_dir.glob("*.log.zip"))

            current_size = sum(f.stat().st_size for f in current_logs if f.is_file())
            compressed_size = sum(f.stat().st_size for f in compressed_logs if f.is_file())

            stats = {
                "current_logs_count": len(current_logs),
                "current_logs_size_mb": round(current_size / 1024 / 1024, 2),
                "compressed_logs_count": len(compressed_logs),
                "compressed_logs_size_mb": round(compressed_size / 1024 / 1024, 2),
                "total_size_mb": round((current_size + compressed_size) / 1024 / 1024, 2),
                "log_dir": str(log_dir),
                "retention_policy": {
                    "main_logs": self.config.main_log_retention,
                    "error_logs": self.config.error_log_retention
                }
            }

            self._stats_cache = stats
            self._cache_time = current_time
            return stats

        except Exception as e:
            logger.error(f"获取日志统计失败: {e}")
            return {}

    def health_check(self):
        """日志系统健康检查"""
        try:
            health_status = {
                "status": "healthy",
                "issues": [],
                "warnings": []
            }

            # 检查日志目录
            if not self.config.log_dir.exists():
                health_status["issues"].append("日志目录不存在")
                health_status["status"] = "error"

            # 检查磁盘空间
            import shutil
            total, used, free = shutil.disk_usage(self.config.log_dir)
            free_gb = free / (1024**3)

            if free_gb < 1:  # 少于1GB
                health_status["issues"].append(f"磁盘空间不足: {free_gb:.2f}GB")
                health_status["status"] = "error"
            elif free_gb < 5:  # 少于5GB
                health_status["warnings"].append(f"磁盘空间较少: {free_gb:.2f}GB")
                if health_status["status"] == "healthy":
                    health_status["status"] = "warning"

            # 检查日志文件数量
            stats = self.get_log_statistics()
            total_logs = stats.get("current_logs_count", 0) + stats.get("compressed_logs_count", 0)

            if total_logs > 100:
                health_status["warnings"].append(f"日志文件数量较多: {total_logs}")
                if health_status["status"] == "healthy":
                    health_status["status"] = "warning"

            health_status["disk_free_gb"] = round(free_gb, 2)
            health_status["total_log_files"] = total_logs

            return health_status

        except Exception as e:
            logger.error(f"日志健康检查失败: {e}")
            return {
                "status": "error",
                "issues": [f"健康检查异常: {str(e)}"],
                "warnings": []
            }

    def cleanup_old_logs(self, days_to_keep: int = None):
        """手动清理旧日志（补充loguru的自动清理）"""
        if days_to_keep is None:
            logger.info("使用loguru自动清理，无需手动清理")
            return {"message": "使用loguru自动清理"}

        try:
            from datetime import datetime, timedelta

            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_timestamp = cutoff_date.timestamp()

            deleted_count = 0
            for log_file in self.config.log_dir.glob("*.log*"):
                if log_file.is_file() and log_file.stat().st_mtime < cutoff_timestamp:
                    log_file.unlink()
                    deleted_count += 1

            logger.info(f"手动清理了 {deleted_count} 个旧日志文件")
            return {"deleted_count": deleted_count, "days_to_keep": days_to_keep}

        except Exception as e:
            logger.error(f"手动清理旧日志失败: {e}")
            return {"error": str(e)}


# 全局日志管理器实例
_log_manager = None


def get_log_manager() -> LogManager:
    """获取全局日志管理器实例"""
    global _log_manager
    if _log_manager is None:
        _log_manager = LogManager()
    return _log_manager


# 【优化版】通用事件日志函数
def log_event(event_type, message, name=None, **kwargs):
    """
    通用事件日志记录。
    现在它能够正确地记录日志发起点的文件名和行号。
    """
    # 获取到的已经是配置好 depth 的 logger 实例
    logger_instance = get_logger(name)
    log_func = getattr(logger_instance, event_type, logger_instance.info)

    # 直接调用即可，AppLogger 内部已经处理了 depth
    log_func(message, **kwargs)
    return logger_instance


# 导出主要接口
__all__ = [
    'AppLogger',
    'get_logger',
    'log_event',
    'default_logger'
] 
# -*- coding: utf-8 -*-
"""
Windows兼容性工具模块
处理Windows特定的文件操作和路径问题
"""
import os
import sys
import time
from pathlib import Path


class WindowsCompatibility:
    """Windows兼容性工具类"""
    
    MAX_PATH_LENGTH = 250 if os.name == 'nt' else 4096
    
    @staticmethod
    def safe_path(path: str | Path) -> Path | None:
        """创建安全的路径，考虑Windows限制"""
        path_obj = Path(path)
        
        if os.name == 'nt' and len(str(path_obj)) > WindowsCompatibility.MAX_PATH_LENGTH:
            # Windows路径过长，返回None
            return None
        
        return path_obj
    
    @staticmethod
    def safe_remove(path: Path, max_retries: int = 3) -> bool:
        """安全删除文件，处理Windows文件锁定"""
        for attempt in range(max_retries):
            try:
                if path.is_file():
                    path.unlink()
                elif path.is_dir():
                    import shutil
                    shutil.rmtree(path)
                return True
            except PermissionError:
                if os.name == 'nt' and attempt < max_retries - 1:
                    time.sleep(0.1 * (attempt + 1))  # 递增等待
                    continue
                return False
            except Exception:
                return False
        
        return False
    
    @staticmethod
    def get_safe_temp_dir() -> Path:
        """获取安全的临时目录"""
        if os.name == 'nt':
            # Windows优先使用用户临时目录
            temp_dirs = [
                Path(os.environ.get('TEMP', '')),
                Path(os.environ.get('LOCALAPPDATA', '')) / "Temp",
                Path("C:/Temp"),
            ]
        else:
            temp_dirs = [
                Path("/tmp"),
                Path(os.path.expanduser("~/.cache")),
            ]
        
        for temp_dir in temp_dirs:
            if temp_dir.exists() and os.access(temp_dir, os.W_OK):
                return temp_dir
        
        # 回退到当前目录
        return Path.cwd() / "temp"


# 全局实例
windows_compat = WindowsCompatibility()

"""
页面操作tracing管理工具
用于记录和管理浏览器操作的回放记录
优化磁盘空间占用问题
"""
import os
import tempfile
import threading
import time
from datetime import datetime
from pathlib import Path

from playwright.async_api import Page

from src.conf.config import CONFIG
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TracingManager:
    """优化的Tracing管理器，解决磁盘空间占用问题"""

    def __init__(self, page: Page):
        self.page = page
        self.trace_dir = Path("./data/traces")
        self.trace_dir.mkdir(exist_ok=True)
        self.current_trace_file = None
        self.is_tracing = False

        # 从配置文件读取磁盘空间优化配置
        tracing_config = CONFIG.Crawler.TRACING
        self.enabled = tracing_config['enabled']
        self.screenshots = tracing_config['screenshots']
        self.snapshots = tracing_config['snapshots']
        self.sources = tracing_config['sources']
        self.max_trace_size_mb = tracing_config['max_trace_size_mb']
        self.max_total_size_gb = tracing_config['max_total_size_gb']
        self.cleanup_interval_hours = tracing_config['cleanup_interval_hours']
        self.keep_days = tracing_config['keep_days']
        self.temp_cleanup_enabled = tracing_config['temp_cleanup_enabled']
        self.force_cleanup_threshold = tracing_config['force_cleanup_threshold']

        # 启动后台清理任务
        self._start_cleanup_thread()

    def _start_cleanup_thread(self):
        """启动后台清理线程"""
        def cleanup_worker():
            while True:
                try:
                    self._cleanup_playwright_temp_files()
                    self._cleanup_old_traces()
                    self._check_disk_usage()
                    time.sleep(self.cleanup_interval_hours * 3600)
                except Exception as e:
                    logger.error(f"清理线程出错: {e}")
                    time.sleep(3600)  # 出错后1小时再试

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.info("已启动trace文件清理线程")

    def _cleanup_playwright_temp_files(self):
        """清理Playwright临时文件"""
        if not self.temp_cleanup_enabled:
            return

        try:
            # Windows系统的Playwright临时目录
            temp_dirs = [
                Path(tempfile.gettempdir()) / "playwright-artifacts",
                Path(os.environ.get('LOCALAPPDATA', '')) / "Temp" / "playwright-artifacts",
                Path(os.environ.get('TEMP', '')) / "playwright-artifacts",
            ]

            # Linux/Mac系统的临时目录
            temp_dirs.extend([
                Path("/tmp/playwright-artifacts"),
                Path(os.path.expanduser("~/.cache/playwright-artifacts")),
            ])

            cleaned_size = 0
            cleaned_files = 0

            for temp_dir in temp_dirs:
                if temp_dir.exists():
                    cleaned_size_dir, cleaned_files_dir = self._cleanup_directory(temp_dir)
                    cleaned_size += cleaned_size_dir
                    cleaned_files += cleaned_files_dir

            if cleaned_files > 0:
                logger.info(f"清理Playwright临时文件: {cleaned_files}个文件, {cleaned_size/1024/1024:.1f}MB")

        except Exception as e:
            logger.error(f"清理Playwright临时文件失败: {e}")

    def _cleanup_directory(self, directory: Path, max_age_hours: int = 24):
        """清理指定目录中的旧文件"""
        cleaned_size = 0
        cleaned_files = 0
        cutoff_time = time.time() - (max_age_hours * 3600)

        try:
            for item in directory.rglob("*"):
                if item.is_file():
                    try:
                        # 检查文件修改时间
                        if item.stat().st_mtime < cutoff_time:
                            file_size = item.stat().st_size
                            item.unlink()
                            cleaned_size += file_size
                            cleaned_files += 1
                    except (OSError, PermissionError) as e:
                        logger.debug(f"无法删除文件 {item}: {e}")
                        continue

            # 清理空目录
            for item in directory.rglob("*"):
                if item.is_dir() and not any(item.iterdir()):
                    try:
                        item.rmdir()
                    except (OSError, PermissionError):
                        pass

        except Exception as e:
            logger.error(f"清理目录 {directory} 失败: {e}")

        return cleaned_size, cleaned_files

    def _cleanup_old_traces(self):
        """清理旧的trace文件"""
        try:
            cutoff_time = time.time() - (self.keep_days * 24 * 3600)  # 使用配置的保留天数
            cleaned_count = 0
            cleaned_size = 0

            for trace_file in self.trace_dir.glob("*.zip"):
                if trace_file.stat().st_mtime < cutoff_time:
                    file_size = trace_file.stat().st_size
                    trace_file.unlink()
                    cleaned_count += 1
                    cleaned_size += file_size

            if cleaned_count > 0:
                logger.info(f"清理旧trace文件: {cleaned_count}个文件, {cleaned_size/1024/1024:.1f}MB")

        except Exception as e:
            logger.error(f"清理旧trace文件失败: {e}")

    def _check_disk_usage(self):
        """检查磁盘使用情况并进行清理"""
        try:
            total_size = sum(f.stat().st_size for f in self.trace_dir.glob("*.zip"))
            total_size_gb = total_size / 1024 / 1024 / 1024

            if total_size_gb > self.max_total_size_gb:
                logger.warning(f"Trace文件总大小超限: {total_size_gb:.1f}GB > {self.max_total_size_gb}GB")
                self._force_cleanup_traces()

        except Exception as e:
            logger.error(f"检查磁盘使用情况失败: {e}")

    def _force_cleanup_traces(self):
        """强制清理trace文件以释放空间"""
        try:
            # 按修改时间排序，删除最旧的文件
            trace_files = list(self.trace_dir.glob("*.zip"))
            trace_files.sort(key=lambda x: x.stat().st_mtime)

            cleaned_count = 0
            cleaned_size = 0
            target_size = self.max_total_size_gb * 0.7 * 1024 * 1024 * 1024  # 清理到70%

            for trace_file in trace_files:
                current_total = sum(f.stat().st_size for f in self.trace_dir.glob("*.zip"))
                if current_total <= target_size:
                    break

                file_size = trace_file.stat().st_size
                trace_file.unlink()
                cleaned_count += 1
                cleaned_size += file_size

            logger.info(f"强制清理trace文件: {cleaned_count}个文件, {cleaned_size/1024/1024:.1f}MB")

        except Exception as e:
            logger.error(f"强制清理trace文件失败: {e}")

    async def start_operation_trace(self, operation_name: str, account_name: str = None):
        """
        开始记录特定操作的trace

        Args:
            operation_name: 操作名称，如 'login', 'fetch_geeks', 'click_next'
            account_name: 账号名称
        """
        # 检查是否启用tracing
        if not self.enabled:
            logger.debug("Tracing已禁用，跳过记录")
            return

        try:
            # 如果已经在记录，先停止当前的trace
            if self.is_tracing:
                await self.stop_current_trace()

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            account_suffix = f"_{account_name}" if account_name else ""
            trace_filename = f"trace_{operation_name}{account_suffix}_{timestamp}.zip"
            self.current_trace_file = self.trace_dir / trace_filename

            # 检查tracing是否已经启动（可能在browser初始化时启动了）
            try:
                # 使用配置文件中的tracing设置
                await self.page.context.tracing.start(
                    screenshots=self.screenshots,
                    snapshots=self.snapshots,
                    sources=self.sources
                )
                logger.info(f"启动优化的trace记录: {operation_name}")
            except Exception as start_error:
                # 如果启动失败，可能是因为已经启动了，尝试重启
                logger.warning(f"Tracing可能已启动，尝试重启: {start_error}")
                try:
                    await self.page.context.tracing.stop()
                    await self.page.context.tracing.start(
                        screenshots=self.screenshots,
                        snapshots=self.snapshots,
                        sources=self.sources
                    )
                    logger.info(f"重启优化trace记录成功: {operation_name}")
                except Exception as restart_error:
                    logger.error(f"重启trace记录失败: {restart_error}")
                    return

            self.is_tracing = True
            logger.info(f"开始记录操作trace: {operation_name}, 文件: {trace_filename}")

        except Exception as e:
            logger.error(f"启动trace记录失败: {e}")
    
    async def stop_current_trace(self):
        """停止当前的trace记录并检查文件大小"""
        try:
            if self.is_tracing:
                if self.current_trace_file:
                    await self.page.context.tracing.stop(path=str(self.current_trace_file))

                    # 检查文件大小
                    if self.current_trace_file.exists():
                        file_size_mb = self.current_trace_file.stat().st_size / 1024 / 1024

                        if file_size_mb > self.max_trace_size_mb:
                            logger.warning(f"Trace文件过大: {file_size_mb:.1f}MB > {self.max_trace_size_mb}MB")
                            # 可以选择删除过大的文件或者压缩
                            # self.current_trace_file.unlink()
                            # logger.info("已删除过大的trace文件")

                        logger.info(f"已保存trace文件: {self.current_trace_file} ({file_size_mb:.1f}MB)")

                    saved_file = str(self.current_trace_file)
                else:
                    # 如果没有指定文件，停止但不保存
                    await self.page.context.tracing.stop()
                    logger.info("已停止trace记录（未保存文件）")
                    saved_file = None

                self.is_tracing = False
                self.current_trace_file = None

                # 立即清理临时文件
                self._cleanup_playwright_temp_files()

                return saved_file
        except Exception as e:
            logger.error(f"停止trace记录失败: {e}")
            self.is_tracing = False
            self.current_trace_file = None
        return None
    
    async def save_error_trace(self, error_type: str, account_name: str = None):
        """
        保存错误时的trace记录
        
        Args:
            error_type: 错误类型
            account_name: 账号名称
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            account_suffix = f"_{account_name}" if account_name else ""
            trace_filename = f"trace_error_{error_type}{account_suffix}_{timestamp}.zip"
            trace_file = self.trace_dir / trace_filename
            
            if self.is_tracing:
                await self.page.context.tracing.stop(path=str(trace_file))
                logger.info(f"已保存错误trace文件: {trace_file}")
                self.is_tracing = False
            else:
                # 如果没有在记录，启动一个新的记录然后立即停止
                await self.page.context.tracing.start(screenshots=True, snapshots=True, sources=True)
                await self.page.context.tracing.stop(path=str(trace_file))
                logger.info(f"已保存错误trace文件: {trace_file}")
            
            return str(trace_file)
        except Exception as e:
            logger.error(f"保存错误trace失败: {e}")
            return None
    
    async def restart_trace(self, operation_name: str, account_name: str = None):
        """
        重启trace记录（先停止当前的，再开始新的）
        
        Args:
            operation_name: 新操作名称
            account_name: 账号名称
        """
        await self.stop_current_trace()
        await self.start_operation_trace(operation_name, account_name)
    
    def cleanup_old_traces(self, days_to_keep: int = 7):
        """
        清理旧的trace文件
        
        Args:
            days_to_keep: 保留天数
        """
        try:
            current_time = time.time()
            cutoff_time = current_time - (days_to_keep * 24 * 60 * 60)
            
            deleted_count = 0
            for trace_file in self.trace_dir.glob("*.zip"):
                if trace_file.stat().st_mtime < cutoff_time:
                    trace_file.unlink()
                    deleted_count += 1
            
            if deleted_count > 0:
                logger.info(f"已清理 {deleted_count} 个旧trace文件")
                
        except Exception as e:
            logger.error(f"清理旧trace文件失败: {e}")


# 全局tracing管理器实例
_tracing_manager = None


def get_tracing_manager(page: Page = None) -> TracingManager:
    """获取全局tracing管理器实例"""
    global _tracing_manager
    if _tracing_manager is None and page:
        _tracing_manager = TracingManager(page)
    return _tracing_manager


async def trace_operation(operation_name: str, account_name: str = None):
    """
    装饰器：自动记录操作的trace
    
    Args:
        operation_name: 操作名称
        account_name: 账号名称
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            tracing_manager = get_tracing_manager()
            if tracing_manager:
                await tracing_manager.start_operation_trace(operation_name, account_name)
            
            try:
                result = await func(*args, **kwargs)
                if tracing_manager:
                    await tracing_manager.stop_current_trace()
                return result
            except Exception as e:
                if tracing_manager:
                    await tracing_manager.save_error_trace(f"{operation_name}_error", account_name)
                raise
        return wrapper
    return decorator


async def start_trace(page: Page, operation_name: str, account_name: str = None):
    """便捷函数：开始trace记录"""
    tracing_manager = get_tracing_manager(page)
    if tracing_manager:
        await tracing_manager.start_operation_trace(operation_name, account_name)


async def stop_trace(page: Page = None):
    """便捷函数：停止trace记录"""
    tracing_manager = get_tracing_manager(page)
    if tracing_manager:
        return await tracing_manager.stop_current_trace()
    return None


async def save_error_trace(page: Page, error_type: str, account_name: str = None):
    """便捷函数：保存错误trace"""
    tracing_manager = get_tracing_manager(page)
    if tracing_manager:
        return await tracing_manager.save_error_trace(error_type, account_name)
    return None

"""
磁盘空间清理工具
专门用于清理Playwright临时文件和trace文件
"""
import os
import shutil
import tempfile
import time
from pathlib import Path
from typing import List, Tuple

import psutil

from src.utils.logger import get_logger

logger = get_logger(__name__)


class DiskCleanupManager:
    """磁盘清理管理器"""
    
    def __init__(self):
        self.playwright_temp_dirs = self._get_playwright_temp_dirs()
    
    def _get_playwright_temp_dirs(self) -> list[Path]:
        """获取所有可能的Playwright临时目录"""
        temp_dirs = []
        
        # Windows系统
        if os.name == 'nt':
            temp_dirs.extend([
                Path(tempfile.gettempdir()) / "playwright-artifacts",
                Path(os.environ.get('LOCALAPPDATA', '')) / "Temp" / "playwright-artifacts",
                Path(os.environ.get('TEMP', '')) / "playwright-artifacts",
                Path(os.environ.get('APPDATA', '')) / "Local" / "Temp" / "playwright-artifacts",
            ])
        
        # Linux/Mac系统
        else:
            temp_dirs.extend([
                Path("/tmp/playwright-artifacts"),
                Path(os.path.expanduser("~/.cache/playwright-artifacts")),
                Path(tempfile.gettempdir()) / "playwright-artifacts",
            ])
        
        # 过滤存在的目录
        return [d for d in temp_dirs if d.exists()]
    
    def get_disk_usage(self) -> dict:
        """获取磁盘使用情况"""
        try:
            # 获取系统磁盘使用情况
            disk_usage = psutil.disk_usage('/')
            if os.name == 'nt':
                disk_usage = psutil.disk_usage('C:')
            
            # 获取Playwright临时文件占用
            playwright_size = 0
            playwright_files = 0
            
            for temp_dir in self.playwright_temp_dirs:
                size, files = self._get_directory_size(temp_dir)
                playwright_size += size
                playwright_files += files
            
            # 获取trace文件占用
            trace_dir = Path("./data/traces")
            trace_size, trace_files = self._get_directory_size(trace_dir) if trace_dir.exists() else (0, 0)
            
            return {
                'total_disk_gb': disk_usage.total / 1024**3,
                'used_disk_gb': disk_usage.used / 1024**3,
                'free_disk_gb': disk_usage.free / 1024**3,
                'disk_usage_percent': (disk_usage.used / disk_usage.total) * 100,
                'playwright_temp_mb': playwright_size / 1024**2,
                'playwright_temp_files': playwright_files,
                'trace_files_mb': trace_size / 1024**2,
                'trace_files_count': trace_files,
                'total_cleanup_mb': (playwright_size + trace_size) / 1024**2
            }
        except Exception as e:
            logger.error(f"获取磁盘使用情况失败: {e}")
            return {}
    
    def _get_directory_size(self, directory: Path) -> tuple[int, int]:
        """获取目录大小和文件数量"""
        total_size = 0
        file_count = 0
        
        try:
            for item in directory.rglob("*"):
                if item.is_file():
                    try:
                        total_size += item.stat().st_size
                        file_count += 1
                    except (OSError, PermissionError):
                        continue
        except Exception as e:
            logger.error(f"计算目录大小失败 {directory}: {e}")
        
        return total_size, file_count
    
    def cleanup_playwright_temp(self, max_age_hours: int = 24) -> dict:
        """清理Playwright临时文件"""
        total_cleaned_size = 0
        total_cleaned_files = 0
        cleaned_dirs = []
        
        cutoff_time = time.time() - (max_age_hours * 3600)
        
        for temp_dir in self.playwright_temp_dirs:
            try:
                cleaned_size, cleaned_files = self._cleanup_directory(temp_dir, cutoff_time)
                if cleaned_files > 0:
                    total_cleaned_size += cleaned_size
                    total_cleaned_files += cleaned_files
                    cleaned_dirs.append(str(temp_dir))
                    logger.info(f"清理 {temp_dir}: {cleaned_files}个文件, {cleaned_size/1024**2:.1f}MB")
            except Exception as e:
                logger.error(f"清理目录失败 {temp_dir}: {e}")
        
        result = {
            'cleaned_size_mb': total_cleaned_size / 1024**2,
            'cleaned_files': total_cleaned_files,
            'cleaned_directories': cleaned_dirs
        }
        
        if total_cleaned_files > 0:
            logger.info(f"Playwright临时文件清理完成: {total_cleaned_files}个文件, {total_cleaned_size/1024**2:.1f}MB")
        
        return result
    
    def _cleanup_directory(self, directory: Path, cutoff_time: float) -> tuple[int, int]:
        """清理目录中的旧文件"""
        cleaned_size = 0
        cleaned_files = 0
        
        try:
            # 清理文件
            for item in directory.rglob("*"):
                if item.is_file():
                    try:
                        if item.stat().st_mtime < cutoff_time:
                            file_size = item.stat().st_size
                            item.unlink()
                            cleaned_size += file_size
                            cleaned_files += 1
                    except (OSError, PermissionError):
                        continue
            
            # 清理空目录
            for item in sorted(directory.rglob("*"), key=lambda x: len(x.parts), reverse=True):
                if item.is_dir() and item != directory:
                    try:
                        if not any(item.iterdir()):
                            item.rmdir()
                    except (OSError, PermissionError):
                        continue
                        
        except Exception as e:
            logger.error(f"清理目录内容失败 {directory}: {e}")
        
        return cleaned_size, cleaned_files
    
    def force_cleanup_all(self) -> dict:
        """强制清理所有Playwright临时文件（不考虑时间）"""
        total_cleaned_size = 0
        total_cleaned_files = 0
        cleaned_dirs = []
        
        for temp_dir in self.playwright_temp_dirs:
            try:
                if temp_dir.exists():
                    size_before, files_before = self._get_directory_size(temp_dir)
                    
                    # 尝试删除整个目录
                    shutil.rmtree(temp_dir, ignore_errors=True)
                    
                    if not temp_dir.exists():
                        total_cleaned_size += size_before
                        total_cleaned_files += files_before
                        cleaned_dirs.append(str(temp_dir))
                        logger.info(f"强制清理 {temp_dir}: {files_before}个文件, {size_before/1024**2:.1f}MB")
                    
            except Exception as e:
                logger.error(f"强制清理目录失败 {temp_dir}: {e}")
        
        result = {
            'cleaned_size_mb': total_cleaned_size / 1024**2,
            'cleaned_files': total_cleaned_files,
            'cleaned_directories': cleaned_dirs
        }
        
        if total_cleaned_files > 0:
            logger.info(f"强制清理完成: {total_cleaned_files}个文件, {total_cleaned_size/1024**2:.1f}MB")
        
        return result
    
    def check_disk_space_critical(self, threshold_percent: float = 90.0) -> bool:
        """检查磁盘空间是否紧张"""
        try:
            usage = self.get_disk_usage()
            return usage.get('disk_usage_percent', 0) > threshold_percent
        except Exception:
            return False
    
    def emergency_cleanup(self) -> dict:
        """紧急清理（磁盘空间不足时）"""
        logger.warning("执行紧急磁盘清理...")
        
        # 1. 强制清理所有Playwright临时文件
        playwright_result = self.force_cleanup_all()
        
        # 2. 清理旧的trace文件
        trace_result = self._cleanup_old_traces(days_to_keep=1)
        
        total_cleaned = playwright_result['cleaned_size_mb'] + trace_result['cleaned_size_mb']
        
        result = {
            'total_cleaned_mb': total_cleaned,
            'playwright_cleanup': playwright_result,
            'trace_cleanup': trace_result
        }
        
        logger.info(f"紧急清理完成，释放空间: {total_cleaned:.1f}MB")
        return result
    
    def _cleanup_old_traces(self, days_to_keep: int = 1) -> dict:
        """清理旧的trace文件"""
        trace_dir = Path("./data/traces")
        if not trace_dir.exists():
            return {'cleaned_size_mb': 0, 'cleaned_files': 0}
        
        cutoff_time = time.time() - (days_to_keep * 24 * 3600)
        cleaned_size = 0
        cleaned_files = 0
        
        try:
            for trace_file in trace_dir.glob("*.zip"):
                if trace_file.stat().st_mtime < cutoff_time:
                    file_size = trace_file.stat().st_size
                    trace_file.unlink()
                    cleaned_size += file_size
                    cleaned_files += 1
        except Exception as e:
            logger.error(f"清理trace文件失败: {e}")
        
        return {
            'cleaned_size_mb': cleaned_size / 1024**2,
            'cleaned_files': cleaned_files
        }


# 全局清理管理器实例
_cleanup_manager = None


def get_cleanup_manager() -> DiskCleanupManager:
    """获取全局清理管理器实例"""
    global _cleanup_manager
    if _cleanup_manager is None:
        _cleanup_manager = DiskCleanupManager()
    return _cleanup_manager


def cleanup_playwright_temp(max_age_hours: int = 24) -> dict:
    """清理Playwright临时文件"""
    manager = get_cleanup_manager()
    return manager.cleanup_playwright_temp(max_age_hours)


def get_disk_usage() -> dict:
    """获取磁盘使用情况"""
    manager = get_cleanup_manager()
    return manager.get_disk_usage()


def emergency_cleanup() -> dict:
    """紧急清理"""
    manager = get_cleanup_manager()
    return manager.emergency_cleanup()


if __name__ == "__main__":
    # 测试功能
    manager = DiskCleanupManager()
    
    print("磁盘使用情况:")
    usage = manager.get_disk_usage()
    for key, value in usage.items():
        print(f"  {key}: {value}")
    
    print("\n清理Playwright临时文件:")
    result = manager.cleanup_playwright_temp()
    for key, value in result.items():
        print(f"  {key}: {value}")

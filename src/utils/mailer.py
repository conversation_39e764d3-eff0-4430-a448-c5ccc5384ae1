# -*- coding: utf-8 -*-
"""
企业微信机器人监控消息发送器
用于发送监控告警、错误报告等消息到企业微信群
"""
import asyncio
import base64
import json
from datetime import datetime
from typing import Optional, Dict, Any

import aiohttp
from playwright.async_api import Page

from src.conf.config import CONFIG
from src.utils.logger import get_logger

logger = get_logger(__name__)

class WeChatWorkBot:
    """企业微信机器人消息发送器"""

    def __init__(self):
        self.webhook_url = CONFIG.WeChatBot.WEBHOOK_URL
        self.timeout = 30  # 请求超时时间
        self.max_retries = 3  # 最大重试次数

    async def send_text_message(self, content: str, mentioned_list: list | None = None) -> bool:
        """
        发送文本消息

        Args:
            content: 消息内容
            mentioned_list: @的用户列表，如 ["@all"] 或 ["user1", "user2"]

        Returns:
            bool: 发送是否成功
        """
        payload = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }

        if mentioned_list:
            payload["text"]["mentioned_list"] = mentioned_list

        return await self._send_request(payload)

    async def send_markdown_message(self, content: str) -> bool:
        """
        发送Markdown格式消息

        Args:
            content: Markdown格式的消息内容

        Returns:
            bool: 发送是否成功
        """
        payload = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }

        return await self._send_request(payload)

    async def send_image_message(self, image_data: bytes) -> bool:
        """
        发送图片消息

        Args:
            image_data: 图片的二进制数据

        Returns:
            bool: 发送是否成功
        """
        # 将图片转换为base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        payload = {
            "msgtype": "image",
            "image": {
                "base64": image_base64,
                "md5": self._calculate_md5(image_data)
            }
        }

        return await self._send_request(payload)

    async def send_monitoring_alert(
        self,
        title: str,
        error_details: str,
        page: Page | None = None,
        severity: str = "warning",
        additional_info: dict[str, Any | None] = None
    ) -> bool:
        """
        发送监控告警消息（包含截图）

        Args:
            title: 告警标题
            error_details: 错误详情
            page: Playwright页面对象（用于截图）
            severity: 严重程度 (info, warning, error, critical)
            additional_info: 额外信息

        Returns:
            bool: 发送是否成功
        """
        try:
            # 构建告警消息
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 严重程度对应的emoji
            severity_emoji = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "critical": "🚨"
            }

            emoji = severity_emoji.get(severity, "⚠️")

            # 构建Markdown消息
            markdown_content = f"""## {emoji} SRA监控告警

**告警时间**: {current_time}
**严重程度**: {severity.upper()}
**告警标题**: {title}

### 错误详情
```
{error_details}
```
"""

            # 添加额外信息
            if additional_info:
                markdown_content += "\n### 附加信息\n"
                for key, value in additional_info.items():
                    markdown_content += f"- **{key}**: {value}\n"

            # 发送文本消息
            success = await self.send_markdown_message(markdown_content)

            # 如果有页面对象，尝试发送截图
            if page and success:
                try:
                    screenshot_data = await self._capture_screenshot(page)
                    if screenshot_data:
                        await asyncio.sleep(1)  # 稍微延迟避免消息发送过快
                        await self.send_image_message(screenshot_data)
                        logger.info("监控告警截图已发送")
                except Exception as e:
                    logger.warning(f"发送截图失败: {e}")

            return success

        except Exception as e:
            logger.error(f"发送监控告警失败: {e}", exc_info=True)
            return False

    async def send_task_notification(
        self,
        task_id: str,
        task_type: str,
        status: str,
        message: str,
        duration: float | None = None,
        account_name: str | None = None
    ) -> bool:
        """
        发送任务通知消息

        Args:
            task_id: 任务ID
            task_type: 任务类型
            status: 任务状态 (success, error, timeout)
            message: 消息内容
            duration: 任务耗时（秒）
            account_name: 账户名称

        Returns:
            bool: 发送是否成功
        """
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 状态对应的emoji
            status_emoji = {
                "success": "✅",
                "error": "❌",
                "timeout": "⏰",
                "warning": "⚠️"
            }

            emoji = status_emoji.get(status, "ℹ️")

            # 构建消息内容
            content = f"""## {emoji} SRA任务通知

**时间**: {current_time}
**任务ID**: {task_id}
**任务类型**: {task_type}
**状态**: {status.upper()}
"""

            if account_name:
                content += f"**账户**: {account_name}\n"

            if duration:
                content += f"**耗时**: {duration:.2f}秒\n"

            content += f"\n**详情**: {message}"

            return await self.send_markdown_message(content)

        except Exception as e:
            logger.error(f"发送任务通知失败: {e}", exc_info=True)
            return False

    async def _capture_screenshot(self, page: Page) -> bytes | None:
        """
        捕获页面截图

        Args:
            page: Playwright页面对象

        Returns:
            bytes: 截图的二进制数据，失败时返回None
        """
        try:
            # 检查页面是否有效
            if not page or page.is_closed():
                logger.error("页面对象无效或已关闭")
                return None

            # 截图配置 - PNG格式不需要quality参数
            screenshot_options = {
                "type": "png",
                "full_page": False,  # 只截取可视区域，避免文件过大
            }

            screenshot_data = await page.screenshot(**screenshot_options)

            # 检查截图大小，企业微信限制图片大小
            if len(screenshot_data) > 2 * 1024 * 1024:  # 2MB限制
                logger.warning(f"截图文件过大: {len(screenshot_data)} bytes，尝试使用JPEG格式压缩")

                # 尝试使用JPEG格式压缩
                try:
                    jpeg_options = {
                        "type": "jpeg",
                        "quality": 60,  # JPEG可以使用quality参数
                        "full_page": False,
                    }
                    screenshot_data = await page.screenshot(**jpeg_options)

                    if len(screenshot_data) > 2 * 1024 * 1024:
                        logger.error(f"压缩后仍然过大: {len(screenshot_data)} bytes")
                        return None

                    logger.info(f"JPEG压缩成功，大小: {len(screenshot_data)} bytes")

                except Exception as compress_error:
                    logger.error(f"JPEG压缩失败: {compress_error}")
                    return None

            logger.info(f"截图捕获成功，大小: {len(screenshot_data)} bytes")
            return screenshot_data

        except Exception as e:
            logger.error(f"捕获截图失败: {e}", exc_info=True)
            return None

    async def _send_request(self, payload: dict[str, Any]) -> bool:
        """
        发送HTTP请求到企业微信webhook

        Args:
            payload: 请求负载

        Returns:
            bool: 发送是否成功
        """
        # logger.info(f"发送消息负载: {payload}")
        if CONFIG.WeChatBot.MOCK:
            return True

        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    async with session.post(
                        self.webhook_url,
                        json=payload,
                        headers={"Content-Type": "application/json"}
                    ) as response:

                        response_text = await response.text()
                        response_data = json.loads(response_text)

                        if response.status == 200 and response_data.get("errcode") == 0:
                            logger.info("企业微信消息发送成功")
                            return True
                        else:
                            error_msg = response_data.get("errmsg", "未知错误")
                            logger.error(f"企业微信消息发送失败: {error_msg}")
                            return False

            except asyncio.TimeoutError:
                logger.warning(f"发送消息超时，第 {attempt + 1} 次尝试")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                    continue
                else:
                    logger.error("发送消息最终超时失败")
                    return False

            except Exception as e:
                logger.error(f"发送消息异常，第 {attempt + 1} 次尝试: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    continue
                else:
                    logger.error("发送消息最终失败")
                    return False

        return False

    def _calculate_md5(self, data: bytes) -> str:
        """计算数据的MD5哈希值"""
        import hashlib
        return hashlib.md5(data).hexdigest()


# 全局企业微信机器人实例
_wechat_bot = None


def get_wechat_bot() -> WeChatWorkBot:
    """获取全局企业微信机器人实例"""
    global _wechat_bot
    if _wechat_bot is None:
        _wechat_bot = WeChatWorkBot()
    return _wechat_bot


# 兼容性函数，保持原有接口
async def send_monitoring_alert(
    title: str,
    error_details: str,
    page: Page | None = None,
    severity: str = "warning"
) -> bool:
    """
    发送监控告警（兼容性函数）

    Args:
        title: 告警标题
        error_details: 错误详情
        page: 页面对象（用于截图）
        severity: 严重程度

    Returns:
        bool: 发送是否成功
    """
    bot = get_wechat_bot()
    return await bot.send_monitoring_alert(title, error_details, page, severity)


async def send_task_notification(
    task_id: str,
    task_type: str,
    status: str,
    message: str,
    **kwargs
) -> bool:
    """
    发送任务通知（兼容性函数）

    Args:
        task_id: 任务ID
        task_type: 任务类型
        status: 状态
        message: 消息
        **kwargs: 其他参数

    Returns:
        bool: 发送是否成功
    """
    bot = get_wechat_bot()
    return await bot.send_task_notification(task_id, task_type, status, message, **kwargs)


if __name__ == "__main__":
    # 测试代码
    async def test_wechat_bot():
        bot = WeChatWorkBot()

        # 测试文本消息
        await bot.send_text_message("SRA系统测试消息")

        # 测试Markdown消息
        await bot.send_markdown_message("""## 测试消息

**时间**: 2024-01-15 10:30:00
**状态**: 测试中

这是一条测试消息。
        """)

    asyncio.run(test_wechat_bot())

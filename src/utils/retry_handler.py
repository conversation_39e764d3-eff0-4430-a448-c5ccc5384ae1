"""
增强的重试处理机制，提供更智能的错误恢复和重试策略
"""
import asyncio
import random
import time
from functools import wraps
from typing import Callable, Any, Optional, List, Dict

from playwright.async_api import TimeoutError, Error as PlaywrightError

from src.utils.logger import get_logger

logger = get_logger(__name__)


class RetryConfig:
    """重试配置类"""
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
        backoff_strategy: str = "exponential"  # exponential, linear, fixed
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
        self.backoff_strategy = backoff_strategy


class RetryableError(Exception):
    """可重试的错误基类"""
    pass


class NetworkError(RetryableError):
    """网络相关错误"""
    pass


class PageLoadError(RetryableError):
    """页面加载错误"""
    pass


class ElementNotFoundError(RetryableError):
    """元素未找到错误"""
    pass


# 定义可重试的异常类型
RETRYABLE_EXCEPTIONS = (
    TimeoutError,
    PlaywrightError,
    NetworkError,
    PageLoadError,
    ElementNotFoundError,
    ConnectionError,
    OSError
)


def calculate_delay(attempt: int, config: RetryConfig) -> float:
    """计算重试延迟时间"""
    if config.backoff_strategy == "exponential":
        delay = config.base_delay * (config.exponential_base ** (attempt - 1))
    elif config.backoff_strategy == "linear":
        delay = config.base_delay * attempt
    else:  # fixed
        delay = config.base_delay
    
    # 应用最大延迟限制
    delay = min(delay, config.max_delay)
    
    # 添加抖动
    if config.jitter:
        jitter_range = delay * 0.1
        delay += random.uniform(-jitter_range, jitter_range)
    
    return max(0, delay)


def retry_async(
    config: RetryConfig | None = None,
    exceptions: tuple = RETRYABLE_EXCEPTIONS,
    on_retry: Callable | None = None
):
    """
    异步重试装饰器
    
    Args:
        config: 重试配置
        exceptions: 可重试的异常类型
        on_retry: 重试时的回调函数
    """
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(1, config.max_attempts + 1):
                try:
                    result = await func(*args, **kwargs)
                    if attempt > 1:
                        logger.info(f"函数 {func.__name__} 在第 {attempt} 次尝试后成功")
                    return result
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts:
                        logger.error(f"函数 {func.__name__} 在 {config.max_attempts} 次尝试后仍然失败: {e}")
                        break
                    
                    delay = calculate_delay(attempt, config)
                    logger.warning(f"函数 {func.__name__} 第 {attempt} 次尝试失败: {e}, {delay:.2f}s 后重试")
                    
                    # 调用重试回调
                    if on_retry:
                        try:
                            await on_retry(attempt, e, delay)
                        except Exception as callback_error:
                            logger.error(f"重试回调函数执行失败: {callback_error}")
                    
                    # 添加人性化的随机延迟
                    human_delay = random.uniform(0.5, 2.0)
                    await asyncio.sleep(delay + human_delay)
                    
                except Exception as e:
                    # 不可重试的异常直接抛出
                    logger.error(f"函数 {func.__name__} 遇到不可重试的异常: {e}")
                    raise
            
            # 所有重试都失败了，抛出最后一个异常
            raise last_exception
        
        return wrapper
    return decorator


class SmartRetryHandler:
    """智能重试处理器"""
    
    def __init__(self):
        self.failure_stats: Dict[str, list[float]] = {}
        self.success_stats: Dict[str, list[float]] = {}
    
    def record_failure(self, operation: str, timestamp: float = None):
        """记录失败"""
        if timestamp is None:
            timestamp = time.time()
        
        if operation not in self.failure_stats:
            self.failure_stats[operation] = []
        
        self.failure_stats[operation].append(timestamp)
        
        # 只保留最近1小时的记录
        cutoff = timestamp - 3600
        self.failure_stats[operation] = [
            t for t in self.failure_stats[operation] if t > cutoff
        ]
    
    def record_success(self, operation: str, timestamp: float = None):
        """记录成功"""
        if timestamp is None:
            timestamp = time.time()
        
        if operation not in self.success_stats:
            self.success_stats[operation] = []
        
        self.success_stats[operation].append(timestamp)
        
        # 只保留最近1小时的记录
        cutoff = timestamp - 3600
        self.success_stats[operation] = [
            t for t in self.success_stats[operation] if t > cutoff
        ]
    
    def get_failure_rate(self, operation: str, window_minutes: int = 30) -> float:
        """获取指定时间窗口内的失败率"""
        now = time.time()
        cutoff = now - (window_minutes * 60)
        
        failures = len([t for t in self.failure_stats.get(operation, []) if t > cutoff])
        successes = len([t for t in self.success_stats.get(operation, []) if t > cutoff])
        
        total = failures + successes
        if total == 0:
            return 0.0
        
        return failures / total
    
    def should_use_aggressive_retry(self, operation: str) -> bool:
        """判断是否应该使用激进的重试策略"""
        failure_rate = self.get_failure_rate(operation)
        return failure_rate > 0.3  # 失败率超过30%时使用激进重试
    
    def get_adaptive_config(self, operation: str) -> RetryConfig:
        """获取自适应的重试配置"""
        if self.should_use_aggressive_retry(operation):
            return RetryConfig(
                max_attempts=5,
                base_delay=2.0,
                max_delay=120.0,
                exponential_base=1.5,
                jitter=True
            )
        else:
            return RetryConfig(
                max_attempts=3,
                base_delay=1.0,
                max_delay=60.0,
                exponential_base=2.0,
                jitter=True
            )


# 全局重试处理器实例
global_retry_handler = SmartRetryHandler()


async def safe_execute_with_retry(
    func: Callable,
    operation_name: str,
    *args,
    **kwargs
) -> Any:
    """
    安全执行函数并自动重试
    
    Args:
        func: 要执行的函数
        operation_name: 操作名称，用于统计
        *args, **kwargs: 传递给函数的参数
    """
    config = global_retry_handler.get_adaptive_config(operation_name)
    
    @retry_async(config=config)
    async def wrapped_func():
        try:
            result = await func(*args, **kwargs)
            global_retry_handler.record_success(operation_name)
            return result
        except Exception as e:
            global_retry_handler.record_failure(operation_name)
            raise
    
    return await wrapped_func()


async def retry_on_failure_callback(attempt: int, exception: Exception, delay: float):
    """重试时的默认回调函数"""
    logger.info(f"重试回调: 第 {attempt} 次尝试失败，异常: {type(exception).__name__}: {exception}")
    logger.info(f"将在 {delay:.2f} 秒后进行下一次尝试")
    
    # 可以在这里添加额外的恢复逻辑
    # 比如清理状态、重置连接等

# config.py
# Shared configuration for the entire architecture.

class Config:
    # --- User/Worker Identifier ---
    USER_ID = "user_X"

    class Redis:
        URL = "redis://:qMdda0ChDwCgR91Bg9tH@************:6800"
        TASK_QUEUE_PREFIX = "playwright_tasks_"
        ADMIN_QUEUE_PREFIX = "playwright_admin_"
        STATUS_KEY_PREFIX = "worker_status:"
        TASK_LOCK_PREFIX = "task_lock:"
        TASK_LOCK_TIMEOUT_SECONDS = 3600  # 1 hour
        RESULT_CHANNEL_PREFIX = "worker_results_"
        TASK_LOCK_TTL_SECONDS = 7200
        WORKER_HEARTBEAT_TIMEOUT_SECONDS = 180

    class SRAA:
        HOST_PORT = "http://sraa.i2soft.net:8300"
        TASK_FILTER_URL = "/task/filter"
        LOGIN_CALLBACK_URL = "/agent/agent_login_callback"
        SYNC_STATUS_URL = "/agent/syncStatus"
        ERROR_CALLBACK_URL = '/task/agent_error_callback'
        SUCCESS_CALLBACK_URL = '/task/agent_success_callback'
        ACTION_CALLBACK_URL = '/task/agent_action_callback'
        SRAA_MOCK = False

    class BossZhiPin:
        WEBSOCKET = {
            "hostname": "ws.zhipin.com",
            "port": 443,
            "path": "/chatws",
            "topic": "chat"
        }
        API = {
            "recommend_url": "https://www.zhipin.com/web/chat/recommend",
            "api_url_pattern": "/wapi/zpjob/rec/geek/list",
            "joblist_url_pattern": "/wapi/zpjob/job/chatted/jobList",
            "search_joblist_url_pattern": "/wapi/zpjob/job/search/job/list",
            "login_url": "https://www.zhipin.com/web/user/?ka=header-login",
            "chat_url": "https://www.zhipin.com/web/chat/index",
            "job_list": "https://www.zhipin.com/web/chat/job/list"
        }

    class FilePaths:
        LOGIN_DATA = "./data/user_{account_name}.json"

    class Crawler:
        PLAYWRIGHT = {
            'browser_type': 'chrome',
            'headless': False
        }
        # 固定用户的基础配置
        ANTI_DETECTION = {
            'simulate_human_behavior': True,
            'stealth_mode': True,
            'fixed_environment': True  # 固定环境模式
        }
        # 固定的延迟配置（模拟熟练用户）
        NEXT_PAGE_DELAY = 30  # 固定延迟
        REQUEST_TIMEOUT = 30  # 固定超时
        PAGE_LOAD_TIMEOUT = 60  # 固定页面加载超时
        CHROME_DRIVER_TYPE = 2

        # 固定用户行为模拟参数
        HUMAN_BEHAVIOR = {
            'scroll_pause_probability': 0.2,  # 降低停顿频率
            'page_browse_probability': 0.15  # 降低页面浏览频率
        }

        # 所有asyncio.sleep相关的延迟配置
        DELAYS = {
            # 页面导航相关
            'page_navigation_delay': 1.0,  # 页面导航前延迟
            'page_load_wait': 2.0,  # 页面加载等待时间

            # 弹窗处理相关
            'dialog_reaction_time': 0.8,  # 用户看到弹窗的反应时间
            'dialog_reading_time': 1.5,  # 用户阅读弹窗内容的时间
            'dialog_close_wait': 0.8,  # 弹窗关闭后等待时间

            # 点击操作相关
            'click_before_delay': 0.5,  # 点击前延迟
            'click_hover_delay': 0.3,  # 悬停延迟
            'click_after_delay': 0.4,  # 点击后延迟

            # 滚动相关
            'scroll_delay_min': 1.0,  # 滚动最小延迟
            'scroll_delay_max': 2.0,  # 滚动最大延迟
            'canvas_scroll_delay_min': 0.5,  # Canvas滚动最小延迟
            'canvas_scroll_delay_max': 1.0,  # Canvas滚动最大延迟
            'reading_pause_min': 1.0,  # 阅读停顿最小时间
            'reading_pause_max': 2.0,  # 阅读停顿最大时间

            # 页面浏览相关
            'page_browse_scroll_wait': 1.5,  # 页面浏览滚动等待
            'page_browse_pause': 2.0,  # 页面浏览停顿

            # 下一页按钮相关
            'next_button_think_time': 1.5,  # 查看当前页面内容的时间
            'next_button_load_wait': 3.0,  # 点击后等待加载时间

            # 网络请求相关
            'request_delay_min': 0.01,  # 请求最小延迟
            'request_delay_max': 0.05,  # 请求最大延迟
            'network_wait_delay': 0.01,  # 网络等待延迟最小值
            'network_wait_delay_max': 0.05,  # 网络等待延迟最大值

            # 错误处理相关
            'error_retry_delay': 0.1,  # 错误重试延迟

            # 休息时间
            'rest_time_min': 3,  # 最小休息时间
            'rest_time_max': 8, # 最大休息时间
        }

        # Tracing配置（优化磁盘空间占用）
        TRACING = {
            'enabled': True,                    # 是否启用tracing
            'screenshots': False,               # 禁用截图以减少文件大小
            'snapshots': True,                  # 保留DOM快照
            'sources': False,                   # 禁用源码记录以减少文件大小
            'max_trace_size_mb': 100,           # 单个trace文件最大10MB
            'max_total_size_gb': 1,            # 总trace文件最大1GB
            'cleanup_interval_hours': 2,       # 每6小时清理一次
            'keep_days': 3,                    # 保留3天的trace文件
            'temp_cleanup_enabled': True,      # 启用临时文件清理
            'force_cleanup_threshold': 0.8,   # 达到80%限制时强制清理
        }
    class WeChatBot:
        WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=94bf57fa-68af-42d0-8736-6070c5f6b2d1"
        MOCK = True

    class Header:
        # httpbin.org 是一个非常有用的 HTTP 请求和响应测试服务。
        # /headers 端点会以 JSON 格式返回它收到的请求头。
        HEADERS_TEST_URL = "https://httpbin.org/headers"
        HEADERS_OUTPUT_FILE = "data/browser_headers.json"  # 定义输出文件名


# Instantiate the config object
CONFIG = Config()
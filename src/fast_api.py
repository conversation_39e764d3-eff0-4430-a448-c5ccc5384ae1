# -*- coding: utf-8 -*-
from fastapi import FastAPI

from routers import ctrl_api, agent_api, dispatch_api
# --- 导入共享配置 ---
# --- 导入 ResultManager (与上一版相同) ---
from src.routers.api_result import setup_result_manager

# --- FastAPI 应用设置 ---
app = FastAPI(
    title=f"SRAA API Panel",
    description="A comprehensive API to manage and interact with a remote Playwright worker.",
    version="4.0.0"
)

# 在应用启动时初始化 ResultManager
setup_result_manager(app)

# --- 包含模块化路由 ---
# 这是关键步骤：将 其他 router 包含到主应用中
app.include_router(ctrl_api.router)
app.include_router(agent_api.router)
app.include_router(dispatch_api.router)


@app.get("/health")
async def health_check():
    """
    健康检查接口，返回服务状态。
    """
    return {"status": "ok"}


# 运行服务 (用于直接启动)
if __name__ == "__main__":
    import uvicorn
    # 建议在生产环境中使用 Gunicorn + Uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

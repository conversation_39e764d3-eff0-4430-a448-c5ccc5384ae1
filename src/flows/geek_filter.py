import asyncio
import json
import random

import requests

from src.conf.config import CONFIG
from src.flows.callback import error_callback
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def geek_filter(batchNo, geek_info, job_detail, isLastPage:bool=False):
    import os
    account_name = os.environ.get('account_name')
    bn_login_name = os.environ.get('bn_login_name')
    request_url = CONFIG.SRAA.HOST_PORT + CONFIG.SRAA.TASK_FILTER_URL

    request_params = {
        "batchNo": batchNo,
        "candidate": geek_info,
        "channelId": "1",
        "recmtUserName": account_name,
        "bnLoginName": bn_login_name,
        "isLastPage": isLastPage,
        "job": job_detail,
    }

    if CONFIG.SRAA.SRAA_MOCK:
        await asyncio.sleep(1)
        probability = random.uniform(0.15, 0.21)  # 随机生成一个 15%~21% 的概率
        status = '3'
        logger.info(f"mock status:{status}")
        return status, '1', ["28e2d7acbe6d9b950nR539y4EFVY"], ["b834e8f5a30948c4b033a8391113995d"]

    logger.info(f"serverurl:{request_url}")
    logger.info(f"request_params:{request_params}")
    rep = requests.post(url=request_url, json=request_params, timeout=(30, 3000))
    logger.info(f'response :{rep.json()}')
    
    if rep.status_code != 200:
        logger.error('访问出错，错误码:%s', rep.status_code)
        logger.info("---进程结束---")
        # 访问网关异常 - 记录触发结果 TODO 需要优化
        # 发送错误回调
        error_callback(batchNo, '访问筛选接口异常中断', '10')
        raise ValueError("SRAA 服务不可用")
    else:
        decoded_response = rep.content.decode("UTF-8")
        data = json.loads(decoded_response)
        code = data["code"]
        message = data["message"]
        
        if code == 1 and message == "服务暂不可用":
            raise ValueError("SRAA 服务不可用")

        if code == 0:
            resultData = data["result"]
            isSkip = resultData.get("isSkip", "0")
            if isSkip == "1":
                logger.info(f'候选人重复了,可能是最后一页了,发起重新筛选')
                # TODO: 候选人重复了，现在是刷新页面来跳过
                return None
                
            status = resultData["status"]
            if status == '':
                status = '0'
            matchprocess = 0
            isContinue = resultData["isContinue"]
            batchStatus = resultData["batchStatus"]
            batchReviewNum = resultData["batchReviewNum"]  # 筛选个数 number
            batchMatchedNum = resultData["batchMatchedNum"]  # 筛中个数 number
            batchHiCandidates = resultData["batchHiCandicateIds"]  # 批量打招呼人员
            batchHiIds = resultData["batchHiIds"]  # 批量打招呼人员

            # 返回基本结果
            return status, isContinue, batchHiCandidates, batchHiIds
        else:
            # 发送错误回调
            error_callback(batchNo, '访问筛选接口异常中断', '10')
            logger.error("code != 0,exit")
            return None


import asyncio
import json
import os
import time
from asyncio import exceptions

from playwright.async_api import TimeoutError

from src.conf.config import CONFIG
from src.core.browser_manager import load_cookies_from_file
from src.flows.callback import sync_login_status, login_callback
from src.flows.geek_fetch_flow import close_dialog, page_goto_and_check
from src.utils.logger import get_logger
from src.utils.tracing_manager import start_trace, stop_trace, save_error_trace

logger = get_logger(__name__)


class BrowserSessionHandler:
    def __init__(self, page, login_data_file: str):
        self.page = page
        self.login_data_file = login_data_file
        self.save_task = None

    async def load(self) -> bool:
        try:
            with open(self.login_data_file, "r", encoding="utf-8") as f:
                login_data = json.load(f)
            await self.page.context.clear_cookies()
            if login_data.get("cookies"):
                await self.page.context.add_cookies(login_data["cookies"])
            # await self.page.evaluate("() => localStorage.clear()")
            # if login_data.get("localStorage"):
            #     for k, v in login_data["localStorage"].items():
            #         await self.page.evaluate(f"() => localStorage.setItem('{k}', '{v}')")
            # await self.page.evaluate("() => sessionStorage.clear()")
            # if login_data.get("sessionStorage"):
            #     for k, v in login_data["sessionStorage"].items():
            #         await self.page.evaluate(f"() => sessionStorage.setItem('{k}', '{v}')")
            logger.info("登录数据已加载")
            return True
        except FileNotFoundError:
            logger.warning("登录文件不存在")
            return False
        except Exception as e:
            logger.error(f"加载失败: {str(e)}")
            return False

    async def save(self) -> bool:
        try:
            directory = os.path.dirname(self.login_data_file)
            os.makedirs(directory, exist_ok=True)
            cookies = await self.page.context.cookies()
            local_storage = await self.page.evaluate("() => Object.assign({}, localStorage)")
            session_storage = await self.page.evaluate("() => Object.assign({}, sessionStorage)")
            login_data = {
                "cookies": cookies,
                "localStorage": local_storage,
                "sessionStorage": session_storage,
                "updateTime": time.time()
            }
            with open(self.login_data_file, "w", encoding="utf-8") as f:
                json.dump(login_data, f, indent=2, ensure_ascii=False)
            logger.info("登录数据已保存到本地文件")
            return True
        except Exception as e:
            logger.error(f"保存失败: {str(e)}")
            raise ValueError(f"cookie保存失败: {e}")

    async def update_and_save(self):
        return await self.save()

    async def start_autosave(self, interval=120):
        # 如果已经有自动保存任务在运行，先停止它
        if self.save_task and not self.save_task.done():
            logger.info("检测到已有自动保存任务，先停止旧任务")
            await self.stop_autosave()

        async def saver():
            while True:
                await self.save()
                await asyncio.sleep(interval)

        self.save_task = asyncio.create_task(saver())
        logger.info("自动保存已启动")

    async def stop_autosave(self):
        if self.save_task:
            self.save_task.cancel()
            try:
                await self.save_task
            except asyncio.CancelledError:
                pass
            logger.info("自动保存已停止")

    async def clear_data(self) -> bool:
        try:
            await self.page.context.clear_cookies()
            await self.page.evaluate("() => localStorage.clear()")
            await self.page.evaluate("() => sessionStorage.clear()")
            logger.info("浏览器内存数据已清空（本地文件未删除）")
            return True
        except Exception as e:
            logger.error(f"清除数据失败: {str(e)}")
            return False

    def delete_local_data(self) -> bool:
        try:
            if os.path.exists(self.login_data_file):
                os.remove(self.login_data_file)
                logger.info(f"本地登录数据文件 {self.login_data_file} 已删除")
            else:
                logger.info(f"本地登录数据文件 {self.login_data_file} 不存在，无需删除")
            return True
        except Exception as e:
            logger.error(f"删除本地登录数据文件失败: {str(e)}")
            return False


async def login_by_qrcode(page):
    """
    只负责获取并返回第一个有效的二维码 URL。
    不再等待登录成功。
    """
    await page.goto(CONFIG.BossZhiPin.API['login_url'])

    # 判断是否需要点击二维码登录切换按钮
    try:
        switch_tip_locator = page.locator('.switch-tip')
        if await switch_tip_locator.is_visible(timeout=3000):
            switch_tip_text = await switch_tip_locator.text_content()
            if switch_tip_text and "APP扫码登录" in switch_tip_text:
                await page.wait_for_selector(".btn-sign-switch", state="visible")
                await page.click(".btn-sign-switch")
                logger.info("检测到APP扫码登录，已点击二维码登录切换按钮")
                await page.wait_for_timeout(1000)
    except Exception as e:
        logger.info(f"二维码切换按钮判断或点击异常: {e}")

    try:
        qr_code_img = await page.wait_for_selector(".qr-img-box img", state="visible", timeout=5000)
        if not qr_code_img:
            logger.info("未找到二维码图片")
            return None
        qr_code_url = await qr_code_img.get_attribute('src')
        if not qr_code_url:
            logger.info("未找到二维码URL")
            return None
        logger.info(f"获取到二维码URL: {qr_code_url}")
        qr_code_url = 'https://www.zhipin.com' + qr_code_url
        return qr_code_url
    except TimeoutError:
        logger.error("登录超时，程序自动退出")
        return None


async def wait_for_login_success(page, manager):
    """
    一个新函数，专门负责在获取到二维码之后，等待用户扫码登录。
    """
    import os
    account_name = os.environ.get('account_name')
    # bn_login_name = os.environ.get('bn_login_name')
    logger.info("正在等待用户扫码登录...")
    timeout = 300  # 等待用户扫码的总超时时间，例如 5 分钟
    start_time = time.time()

    while time.time() - start_time < timeout:
        # 检查是否已登录成功
        try:
            # 使用 locator('...').is_visible() 是非阻塞的检查
            if await page.locator('span.user-name').is_visible():
                logger.info("检测到用户已成功登录！")
                await manager.save()  # 保存登录状态
                await manager.start_autosave(interval=120)  # 2分钟保存一次，减少频率

                if '/web/chat/index' in page.url:
                    # 检查是否被封禁
                    try:
                        limit_boss_dialog_selector = 'div[class*="limit-boss-dialog"] div.limit-content p'
                        limit_boss_dialog_locator = await page.locator(limit_boss_dialog_selector)
                        dialog_count = limit_boss_dialog_locator.count()
                        logger.error(f"找到了 {dialog_count} 个匹配的对话框段落。")
                        if dialog_count == 2:
                            all_texts = limit_boss_dialog_locator.all_inner_texts()
                            context1_alt, context2_alt = all_texts
                            logger.error(f"当前账号被封，信息: {context1_alt}, {context2_alt}")
                            login_callback(action="blocked", blockedContext=context2_alt)
                    except Exception as e:
                        logger.info("没有弹出提示框，忽略")

                await close_dialog(page)

                user_name = page.locator('span.user-name').text_content()

                if user_name != account_name and user_name[0] != account_name[0]:
                    logger.error(
                        f'传入的用户名和扫码登录的用户不一致,上送招聘助手sraa服务器检查用户名，username={user_name}')
                    login_callback(action="check_external_system_username", externalSystemUsername=user_name)
                    return False

                cookies_json = load_cookies_from_file(account_name=account_name, return_type=2)
                sync_login_status(cookies=cookies_json, status='1')
                return True  # 返回成功状态

        except Exception:
            pass  # 忽略检查过程中的异常

        # 检查二维码是否已过期并需要刷新
        try:
            # 使用 is_visible() 避免阻塞
            if await page.locator('[ka="refresh_app_sao_qrcode"]').is_visible(timeout=1000):
                logger.info("检测到二维码已过期，进行刷新操作")
                await page.click('[ka="refresh_app_sao_qrcode"]')
                qr_code_img = await page.wait_for_selector(".qr-img-box img", state="visible", timeout=10000)
                if qr_code_img:
                    new_qr_code_url = await qr_code_img.get_attribute('src')
                    new_qr_code_url = 'https://www.zhipin.com' + new_qr_code_url
                    logger.info(f"二维码刷新成功，新URL: {new_qr_code_url}")
                    login_callback(action="refresh_qrcode", qrCodeUrl=new_qr_code_url)
                else:
                    logger.info("刷新后未找到二维码元素")
        except Exception:
            logger.error("刷新二维码检查失败，继续")
            pass

        await asyncio.sleep(2)  # 每 2 秒检查一次状态

    logger.error("等待用户扫码登录超时。")
    return False


async def login(page, username):
    """
    改造后的版本：
    1. 尝试 cookie 登录。
    2. 如果失败，调用改造后的 login_by_qrcode 获取二维码URL。
    3. 返回一个包含 manager 和 qr_code_url 的元组。
    """
    try:
        # 启动登录操作的tracing
        await start_trace(page, "login", username)

        account_name = username
        login_file = CONFIG.FilePaths.LOGIN_DATA.format(account_name=account_name)
        manager = BrowserSessionHandler(page, login_file)

        try:
            await page.locator('span.user-name').wait_for(timeout=3000)
            logger.info("已登录...")
            return manager, None
        except TimeoutError:
            logger.warning("当前未登录，进行登录操作...")

        if os.path.exists(login_file):
            logger.info("尝试使用本地 Cookie 登录...")
            if await manager.load():
                await page_goto_and_check(CONFIG.BossZhiPin.API['chat_url'], page)
                await page.wait_for_timeout(5000)
                try:
                    await page.locator('span.user-name').wait_for(timeout=5000)
                    logger.info("Cookie 自动登录成功。")
                    await manager.start_autosave()
                    # 返回 manager 和 None 表示已登录，无需二维码
                    return manager, None
                except TimeoutError:
                    logger.error("Cookie 已失效，需要二维码登录。")

        # Cookie 登录失败或无 Cookie，执行二维码登录流程
        qr_code_url = await login_by_qrcode(page)

        # 登录流程完成，停止tracing
        await stop_trace(page)

        # 返回 manager 和获取到的二维码 URL
        return manager, qr_code_url

    except Exception as e:
        # 登录过程中出错，保存错误trace
        await save_error_trace(page, "login_error", username)
        logger.error(f"登录过程中发生错误: {e}", exc_info=True)
        raise


async def check_cookie(page):
    cookies = await page.context.cookies()
    cookies_json = json.loads(cookies)
    if cookies_json['wt2'] is None:
        raise exceptions.InvalidStateError('cookie 状态发生变化')
    else:
        return True
# src/flows/validation_flow.py

from playwright.async_api import Page, Frame

from src.core.browser_manager import simulate_canvas_scroll
from src.utils.logger import get_logger

logger = get_logger(__name__)

class ValidationFailedError(Exception):
    """自定义异常，用于清晰地表示结构验证失败。"""
    pass

async def perform_structural_validation(page: Page, selectors: dict) -> (Frame, Frame):
    """
    执行标准化的、分阶段的页面结构验证。
    这是预检和基线生成共用的核心验证逻辑。

    Args:
        page: Playwright Page 对象。
        selectors: 包含所有需要验证的选择器的字典。

    Returns:
        一个元组 (recommend_frame, resume_frame)，包含验证通过的Frame对象。

    Raises:
        ValidationFailedError: 如果任何阶段的验证失败。
    """
    try:
        # 阶段 1: 验证主推荐页和 recommendFrame
        logger.info("验证阶段 1: 主推荐iframe...")
        await page.wait_for_selector(selectors["RECOMMEND_FRAME"], timeout=15000)
        recommend_frame = page.frame(name="recommendFrame")
        if not recommend_frame: raise Exception("无法获取 recommendFrame 的上下文。")
        logger.success("[通过] 主推荐iframe存在。")

        # 阶段 2: 验证列表视图
        logger.info("验证阶段 2: 列表视图...")
        await recommend_frame.wait_for_selector(selectors["CANDIDATE_CARD"], timeout=7000)
        await recommend_frame.wait_for_selector(selectors["JOB_DROPDOWN"], timeout=7000)
        logger.success("[通过] 列表视图核心元素存在。")

        # 阶段 3: 验证详情视图
        logger.info("验证阶段 3: 详情视图...")
        await recommend_frame.click(selectors["CANDIDATE_CARD"], timeout=5000)
        await page.wait_for_timeout(3000)

        # 使用循环来验证所有详情视图内的元素，提高可读性和可维护性
        detail_view_selectors = {
            "NEXT_CANDIDATE_BUTTON": selectors["NEXT_CANDIDATE_BUTTON"],
            "RESUME_SUMMARY_DIV": selectors["RESUME_SUMMARY_DIV"],
            "RESUME_FRAME": selectors["RESUME_FRAME"],
            "LIKE_BUTTON": selectors["LIKE_BUTTON"],
            "WORK_EXP_LIST": selectors["WORK_EXP_LIST"],
            # 项目和教育经历可能不是每个候选人都有，但我们要求这个结构必须存在
            "PROJECT_EXP_LIST": selectors["PROJECT_EXP_LIST"],
            "EDUCATION_EXP_LIST": selectors["EDUCATION_EXP_LIST"],
        }

        for key, selector in detail_view_selectors.items():
            await recommend_frame.wait_for_selector(selector, timeout=7000)
            logger.success(f"[通过] 详情视图元素: '{selector}' ({key})")

        logger.success("[通过] 详情视图所有核心元素均已验证。")

        # 阶段 4: 验证嵌套的简历iframe和Canvas
        logger.info("验证阶段 4: 嵌套的简历iframe和Canvas...")
        inner_frame_handle = await recommend_frame.query_selector(selectors["RESUME_FRAME"])
        resume_frame: Frame = await inner_frame_handle.content_frame()
        if not resume_frame: raise Exception("无法获取 resume_frame 的上下文。")
        await resume_frame.wait_for_selector(selectors["RESUME_CANVAS"], timeout=5000)
        logger.success("[通过] 嵌套的简历Canvas存在。")

        # 阶段 5: 滚动Canvas以验证其可交互性和完整加载能力
        logger.info("验证阶段 5: 滚动Canvas...")
        await simulate_canvas_scroll(page, resume_frame)
        logger.success("[通过] Canvas滚动完成，内容可完整加载。")

        return recommend_frame, resume_frame

    except Exception as e:
        # 将所有底层的Playwright超时或其他异常，统一包装成我们自己的验证异常
        logger.error(f"页面结构验证失败: {e}", exc_info=True)
        raise ValidationFailedError(f"页面结构验证失败: {e}")

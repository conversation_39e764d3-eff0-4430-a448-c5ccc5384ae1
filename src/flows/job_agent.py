# -*- coding: utf-8 -*-
"""
职位代理模块 - 重构版
将原有的直接URL请求改造为通过celery_worker.py的action机制处理
"""
import json
import time
import uuid
from typing import Dict, Any, Optional
from playwright.async_api import Page

from src.conf.config import CONFIG
from src.utils.logger import get_logger
from src.routers.api_result import get_result_manager

logger = get_logger(__name__)


class JobAgent:
    """职位代理类 - 通过action机制处理各种职位相关操作"""

    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.result_manager = get_result_manager()

    async def _send_task_and_wait(
        self,
        action: str,
        payload: Dict[str, Any],
        timeout: int = 60
    ) -> Dict[str, Any]:
        """
        发送任务到worker并等待结果

        Args:
            action: 任务动作类型
            payload: 任务负载数据
            timeout: 超时时间（秒）

        Returns:
            Dict: 任务执行结果
        """
        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 构建任务负载
        task_payload = {
            "id": task_id,
            "action": action,
            "result_channel": f"{CONFIG.Redis.RESULT_CHANNEL_PREFIX}{CONFIG.USER_ID}",
            "payload": payload,
            "timestamp": time.time()
        }

        logger.info(f"发送任务: {action}, ID: {task_id}")

        try:
            # 发送任务到Redis队列
            self.redis_client.lpush(
                f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.USER_ID}",
                json.dumps(task_payload)
            )

            # 等待任务结果
            result = await self.result_manager.wait_for_result(task_id, timeout=timeout)

            logger.info(f"任务完成: {action}, ID: {task_id}, 状态: {result.get('status')}")
            return result

        except Exception as e:
            logger.error(f"任务执行失败: {action}, ID: {task_id}, 错误: {e}")
            return {
                "status": "error",
                "id": task_id,
                "message": f"任务执行失败: {str(e)}"
            }

    async def get_job_list(self) -> Dict[str, Any]:
        """
        获取职位列表

        Returns:
            Dict: 职位列表数据
        """
        payload = {}

        return await self._send_task_and_wait("get_job_list", payload, timeout=30)



# 全局实例
_job_agent_instance = None


def get_job_agent(redis_client) -> JobAgent:
    """获取JobAgent实例"""
    global _job_agent_instance
    if _job_agent_instance is None:
        _job_agent_instance = JobAgent(redis_client)
    return _job_agent_instance


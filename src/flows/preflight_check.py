# src/flows/preflight_check.py
import json
from pathlib import Path

from playwright.async_api import Page

from src.conf.config import CONFIG
from src.flows.geek_fetch_flow import close_dialog
from src.flows.geek_info_build import _extract_and_rebuild_text
from src.flows.validation_flow import perform_structural_validation, ValidationFailedError
from src.utils.logger import get_logger

logger = get_logger(__name__)

class PreflightCheckError(Exception):
    """自定义异常，用于表示预检失败。"""
    def __init__(self, message, mismatches):
        super().__init__(message)
        self.mismatches = mismatches

async def run_preflight_check(page: Page):
    """
    【V3 - 重构版】
    执行页面结构和Canvas结构的预检。核心验证逻辑已移至 validation_flow.py。

    Args:
        page: Playwright Page 对象。

    Raises:
        PreflightCheckError: 如果检测到任何与基线不匹配的情况。
    """
    logger.info("--- 开始执行页面结构预检 (V3-重构版) ---")
    baseline_path = Path("baseline.json")
    if not baseline_path.exists():
        raise FileNotFoundError("未找到基线文件 baseline.json。请先运行 create_baseline.py 生成。")

    with open(baseline_path, "r", encoding="utf-8") as f:
        baseline = json.load(f)

    mismatches = []

    try:
        # 1. 执行共享的、标准化的结构验证
        await page.goto(CONFIG.BossZhiPin.API['recommend_url'], wait_until="domcontentloaded")
        await close_dialog(page)
        _, resume_frame = await perform_structural_validation(page, baseline["selectors"])
        logger.success("[通过] 核心页面结构与元素验证成功。")

        # 2. 执行预检独有的任务：Canvas 文本结构检查
        logger.info("正在提取Canvas文本以进行结构分析...")
        ## TODO 修复代码
        canvas_text = await _extract_and_rebuild_text(resume_frame, '')
        if not canvas_text:
            mismatches.append("无法从Canvas中提取任何文本内容。")
        else:
            missing_keywords = [
                kw for kw in baseline["canvas_structure"]["expected_section_keywords"]
                if kw not in canvas_text
            ]
            if not missing_keywords:
                logger.success("[通过] Canvas结构检查通过，所有预期的板块关键词都已找到。")
            else:
                mismatches.append(f"Canvas结构检查失败，缺失以下板块关键词: {', '.join(missing_keywords)}")

    except ValidationFailedError as e:
        # 捕获来自共享模块的特定异常
        mismatches.append(str(e))
    except Exception as e:
        # 捕获此模块自身的其他异常
        mismatches.append(f"预检过程中发生未知错误: {e}")

    # 3. 最终报告
    if mismatches:
        logger.error("--- 预检失败 ---")
        raise PreflightCheckError("预检失败：页面或Canvas结构与基线不匹配。", mismatches)
    else:
        logger.success("--- 所有预检项目均已通过 ---")

## 项目业务说明书

## 基础环境
### python3.11
#### 性能提升
整体性能: 10-15%提升  
异步操作: 更快的asyncio性能  
内存使用: 更好的内存管理  
#### 开发体验
更现代的语法: 海象操作符、新类型注解  
更好的错误信息: Python 3.11改进的错误提示  
更强的类型检查: 更好的IDE支持  
### Windows支持
更好的路径处理: 增强的Windows兼容性
更稳定的文件操作: 改进的错误处理
更好的性能: Windows特定优化

## 1. 项目概述

本项目是一个基于 **Playwright** 和 **FastAPI** 的自动化招聘辅助系统（SRA - Smart Recruiting Assistant），旨在模拟用户在“Boss直聘”等招聘网站上的操作，实现自动化筛选、沟通和管理候选人。系统核心是一个由 **Celery**（或类似的异步任务队列）驱动的 **Playwright Worker**，它负责执行浏览器自动化任务。**FastAPI** 则提供了一套完整的Web API，用于控制Worker、分发任务、查询状态以及与外部系统（如SRAA服务器）进行数据交互。

### 2. 核心业务流程

系统的核心业务是**自动化筛选候选人**。其基本流程如下：

1.  **登录**: 系统首先会尝试使用本地缓存的Cookie进行自动登录。如果Cookie失效或不存在，系统会获取登录二维码，并通过API返回给前端，等待用户扫码登录。登录成功后，会话信息（Cookie等）将被保存，并启动一个定时任务自动刷新和保存会话，以维持登录状态。
2.  **任务触发**: 外部系统（或用户）通过调用FastAPI的 `/agent/jobfilter/trigger` 接口，传入职位ID (`jobId`)、批次号 (`batchNo`) 等参数，来启动一个筛选任务。
3.  **任务分发**: FastAPI接收到请求后，会将任务封装成一个消息，推送到 **Redis** 的任务队列中。
4.  **Worker执行**: Playwright Worker 持续监听Redis任务队列。当接收到新任务后，它会：
    a.  启动或复用一个浏览器实例。
    b.  导航到“Boss直聘”的推荐牛人页面。
    c.  根据任务中的 `jobId`，在页面上选择对应的职位。
    d.  开始模拟人类行为（如滚动页面）来加载推荐的候选人列表。
    e.  通过拦截API请求和解析前端渲染数据，获取候选人的详细信息。
5.  **候选人信息解析与融合**:
    *   系统会同时捕获“Boss直聘”后端API返回的JSON数据和前端通过Canvas绘制的简历文本。
    *   一个复杂的`HybridStructureExtractor`（混合结构提取器）会将这两种来源的数据进行智能融合，提取出包括工作经历、项目经验、教育背景、期望职位、技能等在内的结构化简历信息。
    *   这个过程特别处理了Canvas文本提取的难点，通过坐标分析、去重、行重建等算法，将零散的字符还原成连贯的文本段落。
6.  **筛选与回调**:
    *   解析出的结构化简历数据会被发送到外部的SRAA服务器进行评估（通过 `geek_filter` 函数）。
    *   SRAA服务器返回筛选结果（如“继续”、“停止”、“收藏”等）。
    *   Worker根据返回结果执行相应操作，例如：点击“下一个”候选人、收藏候选人，或者在满足特定条件时结束整个任务。
7.  **状态同步与回调**: 在整个流程中，Worker会不断地将任务状态（如：运行中、已完成、错误）、登录状态、心跳等信息更新到Redis，并通过回调URL将任务的最终结果（成功或失败）通知给SRAA服务器。

### 3. 技术架构与模块说明

*   **`fast_api.py`**: 项目的Web入口，使用FastAPI框架。它整合了各个路由模块，提供了一个统一的API服务。
*   **`celery_worker.py`**: 核心的浏览器自动化工作进程。它负责从Redis任务队列中获取任务，并使用Playwright执行具体的浏览器操作。它还包含一个管理循环，可以响应如“暂停”、“恢复”、“重启”等管理指令。
*   **`routers/`**: API路由模块。
    *   `agent_api.py`: 提供了面向外部系统（Agent）的主要接口，如登录、获取职位列表、触发筛选任务等。
    *   `ctrl_api.py`: 提供了对Worker的控制接口，如查询状态、发送管理指令。
    *   `dispatch_api.py`: 提供了通用的任务分发接口（同步/异步）。
    *   `api_result.py`: 实现了一个精巧的结果管理器，允许API在分发任务后，能同步等待并获取异步Worker的执行结果。
*   **`flows/`**: 核心业务逻辑流模块。
    *   `login.py`: 封装了完整的登录逻辑，包括Cookie登录和二维码登录。
    *   `geek_fetch_flow.py`: 实现了抓取和处理候选人列表的核心流程。
    *   `geek_info_build.py`: 包含了高度复杂的`HybridStructureExtractor`类，负责从API数据和Canvas渲染文本中提取和融合简历信息。
    *   `geek_filter.py`: 调用外部SRAA服务进行候选人筛选。
    *   `callback.py`: 封装了所有与外部SRAA服务器的回调接口（如成功、失败、登录状态同步）。
*   **`core/`**: 核心底层支持模块。
    *   `browser_manager.py`: 负责初始化和管理Playwright浏览器实例，包括设置请求拦截、注入JS Hook脚本以捕获Canvas绘制数据、模拟人类滚动行为等。
*   **`conf/config.py`**: 全局配置文件，集中管理了Redis地址、API URL、文件路径等所有配置项。
*   **`utils/logger.py`**: 日志模块，使用`loguru`库提供强大且易于配置的日志记录功能。

### 4. Mermaid 流程图

```mermaid
graph TD
    subgraph "用户/外部系统"
        A[触发筛选任务 API] --> B{FastAPI 服务};
    end

    subgraph "FastAPI 应用层"
        B -- 1. 封装任务 --> C[Redis 任务队列];
        B -- 6. 等待并返回结果 --> A;
    end

    subgraph "Playwright Worker (celery_worker.py)"
        D[Worker进程] -- 2. 监听并获取任务 --> C;
        D -- 3. 执行任务 --> E{核心抓取流程};
    end

    subgraph "核心抓取与解析流程 (flows/)"
        E -- a. 登录 --> F[Login Flow];
        F -- b. 导航到推荐页 --> G[Geek Fetch Flow];
        G -- c. 模拟滚动/点击 --> H[加载候选人列表];
        H -- d. 拦截API & 捕获Canvas --> I[获取原始数据];
        I -- e. 数据融合解析 --> J[HybridStructureExtractor];
        J -- f. 结构化简历 --> K[Geek Filter];
        K -- g. 调用外部SRAA筛选 --> L[SRAA服务器];
        L -- h. 返回筛选指令 --> K;
        K -- i. 执行指令(下一个/停止) --> G;
    end

    subgraph "数据与状态管理"
        M[Redis]
        C -- 任务存储 --> M;
        D -- 更新Worker状态 --> M;
        E -- 回调通知 --> L;
        B -- 监听结果 --> M;
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#ccf,stroke:#333,stroke-width:2px
    style L fill:#f9f,stroke:#333,stroke-width:2px
```
